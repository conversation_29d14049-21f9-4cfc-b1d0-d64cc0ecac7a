\documentclass{xduugthesis}
\usepackage{booktabs}
\usepackage{multirow}  % 支持表格中的多行合并
\usepackage{amsmath}
\usepackage{amssymb}  % 支持 \mathbb 命令
\usepackage{graphicx}
\usepackage{listings}
\usepackage[backend=biber,style=gb7714-2015]{biblatex} % 添加 biblatex 包

% 添加字体配置，使用 Times New Roman 作为英文、数字和半角标点的字体
\usepackage{fontspec}
\setmainfont{Times New Roman}  % 设置主要字体为 Times New Roman
\setsansfont{Times New Roman}  % 设置无衬线字体为 Times New Roman
\setmonofont{Times New Roman}  % 设置等宽字体为 Times New Roman

% 确保英文摘要使用 Times New Roman
\newfontfamily\englishfont{Times New Roman}
\newcommand{\english}[1]{{\englishfont #1}}

% 使用标准数学字体，而不是尝试使用不存在的 Times New Roman Math
% \usepackage{unicode-math}
% \setmathfont{Times New Roman Math}  % 这行导致错误，已注释掉

\lstset{
  basicstyle=\small\ttfamily,
  breaklines=true,
  frame=single,
  numbers=left,
  numberstyle=\tiny,
  keywordstyle=\color{blue},
  commentstyle=\color{gray},
  stringstyle=\color{green},
  tabsize=4
}

% 确保参考文献资源被加载
\addbibresource{ref.bib}

% 盲审版本设置 - 移除致谢部分
\xdusetup{
  info = {
    title = {基于 MultiVI 的单细胞多组学数据集成方法研究及实现},
    author = {}, % 隐去作者姓名
    department = {计算机科学与技术学院},
    major = {软件工程},
    class-id = {}, % 隐去班级
    student-id = {}, % 隐去学号
    supervisor = {}, % 隐去导师姓名
    bib-resource = {ref.bib},
    abstract = {chapters/abstract.tex},
    abstract* = {chapters/abstract_en.tex},
    keywords = {单细胞多组学，数据集成，MultiVI，深度学习},
    keywords* = {Single-cell Multi-omics, Data Integration, MultiVI, Deep Learning}
    % 删除致谢部分
    }
  }

% 在封面左上角添加序号（请替换X为您的序号）
\usepackage{eso-pic}
\AddToShipoutPictureBG*{%
  \AtPageUpperLeft{%
    \hspace{1cm}\raisebox{-1cm}{%
      \fontsize{10.5pt}{12.6pt}\selectfont\bfseries 计-241 % 使用五号字体
    }%
  }%
}

% 修改页眉字号为五号
\usepackage{fancyhdr}
\pagestyle{fancy}
\fancyhf{}
\fancyhead[CE]{\fontsize{10.5pt}{12.6pt}\selectfont\leftmark}
\fancyhead[CO]{\fontsize{10.5pt}{12.6pt}\selectfont 基于MultiVI的单细胞多组学数据集成方法研究及实现}
\fancyfoot[C]{\fontsize{10.5pt}{12.6pt}\selectfont\thepage}
\renewcommand{\headrulewidth}{0.4pt}
\renewcommand{\footrulewidth}{0pt}

% 设置摘要、ABSTRACT和目录页的页眉
\fancypagestyle{plain}{%
  \fancyhf{}%
  \fancyhead[CE]{\fontsize{10.5pt}{12.6pt}\selectfont\leftmark}%
  \fancyhead[CO]{\fontsize{10.5pt}{12.6pt}\selectfont 基于MultiVI的单细胞多组学数据集成方法研究及实现}%
  \fancyfoot[C]{\fontsize{10.5pt}{12.6pt}\selectfont\thepage}%
  \renewcommand{\headrulewidth}{0.4pt}%
  \renewcommand{\footrulewidth}{0pt}%
}

\begin{document}

% 重置页码
\pagenumbering{arabic}
\setcounter{page}{1}

% 包含前三章内容
\include{chapters/intro}
\include{chapters/theory}
\include{chapters/method}
% 第四章内容 - 修正版
\chapter{实验结果与分析}

\section{实验环境}
本研究的实验环境配置如下：

硬件环境:
\begin{itemize}
  \item CPU: 11th Gen Intel(R) Core(TM) i7-11800H @ 2.30GHz
  \item GPU: NVIDIA GeForce RTX 3060 6GB
  \item RAM: 32GB DDR4
\end{itemize}

软件环境:
\begin{itemize}
  \item 操作系统: Windows 11 Pro
  \item Python 版本: 3.10.9
  \item 主要库版本: PyTorch 2.0.1, Scanpy 1.9.3, AnnData 0.9.1, scvi-tools 0.20.3
\end{itemize}

\section{数据集}

本研究使用了以下四个公开可用的单细胞多组学数据集：

\begin{enumerate}
  \item \textbf{10x\_lymph\_node}: 包含人类淋巴结组织的10x Multiome数据，共有10,412个细胞，33,538个基因和215,872个染色质区域。

  \item \textbf{snare\_p0}: 小鼠大脑P0阶段的SNARE-seq数据，包含1,047个细胞，24,676个基因和15,240个染色质区域。

  \item \textbf{paired\_cellline}: 人类细胞系的配对scRNA-seq和scATAC-seq数据，包含2,789个细胞，3,000个基因和109,647个染色质区域。

  \item \textbf{sciCAR\_cellline}: 使用sci-CAR技术测量的人类细胞系数据，包含4,825个细胞，26,593个基因和94,324个染色质区域。
\end{enumerate}

表\ref{tab:datasets_info}总结了各数据集的基本信息，包括细胞数量、基因数量、染色质区域数量和数据类型等关键特征。

\input{tables/table_1}

\section{评估方法}
本研究采用了以下实验方法来评估MultiVI算法的性能：

\begin{enumerate}
  \item 数据预处理：对原始数据进行质量控制、标准化和特征选择。
  \item 模型训练：使用不同的超参数配置训练MultiVI模型。
  \item 性能评估：使用多种指标评估模型在细胞聚类和批次效应校正方面的表现。
  \item 算法对比：与其他主流算法进行对比分析。
\end{enumerate}

\section{实验结果}

\subsection{模型训练与收敛}

MultiVI模型在不同数据集上的训练时间和收敛情况如下：

\begin{itemize}
  \item 10x\_lymph\_node: 训练时间38.6分钟，收敛轮数342，最终验证损失1.763
  \item snare\_p0: 训练时间15.2分钟，收敛轮数298，最终验证损失2.047
  \item paired\_cellline: 训练时间17.8分钟，收敛轮数312，最终验证损失1.892
  \item sciCAR\_cellline: 训练时间16.5分钟，收敛轮数305，最终验证损失1.924
\end{itemize}

我们发现，模型训练在RTX 3060 GPU上能够高效完成，相比CPU训练提高了显著的速度。

为了评估模型在不同分辨率参数下的性能表现，我们分析了聚类指标与分辨率参数的关系。图\ref{fig:10x_metrics}和图\ref{fig:snare_metrics}分别展示了10x\_lymph\_node和snare\_p0数据集在不同分辨率下的评估指标变化趋势，帮助我们选择最优的聚类参数。

\begin{figure}[htbp]
\centering
\includegraphics[width=0.8\textwidth]{images/10x_lymph_node_metrics_vs_resolution.png}
\caption{10x\_lymph\_node数据集聚类指标与分辨率参数的关系}
\label{fig:10x_metrics}
\end{figure}

\begin{figure}[htbp]
\centering
\includegraphics[width=0.8\textwidth]{images/snare_p0_metrics_vs_resolution.png}
\caption{snare\_p0数据集聚类指标与分辨率参数的关系}
\label{fig:snare_metrics}
\end{figure}

\subsection{细胞聚类与评估}

MultiVI在不同数据集上的聚类性能评估结果如下：

\begin{itemize}
  \item 10x\_lymph\_node: ARI=0.843, NMI=0.897, V-measure=0.882
  \item snare\_p0: ARI=0.762, NMI=0.815, V-measure=0.798
  \item paired\_cellline: ARI=0.921, NMI=0.945, V-measure=0.937
  \item sciCAR\_cellline: ARI=0.835, NMI=0.876, V-measure=0.864
\end{itemize}

表\ref{tab:multivi_performance}详细展示了MultiVI在四个数据集上的完整性能评估结果，包括聚类准确性、批次效应校正和计算效率等多个维度的指标。

\input{tables/table_3}

为了直观展示MultiVI的聚类效果，我们使用UMAP降维技术对集成后的数据进行可视化。图\ref{fig:10x_umap_celltype}和图\ref{fig:snare_umap_celltype}分别展示了10x\_lymph\_node和snare\_p0数据集按细胞类型着色的UMAP图，可以看出不同细胞类型在低维空间中形成了清晰的聚类。

\begin{figure}[htbp]
\centering
\includegraphics[width=0.8\textwidth]{images/10x_lymph_node_umap_celltype.png}
\caption{10x\_lymph\_node数据集按细胞类型着色的UMAP可视化}
\label{fig:10x_umap_celltype}
\end{figure}

\begin{figure}[htbp]
\centering
\includegraphics[width=0.8\textwidth]{images/snare_p0_umap_celltype.png}
\caption{snare\_p0数据集按细胞类型着色的UMAP可视化}
\label{fig:snare_umap_celltype}
\end{figure}

图\ref{fig:10x_umap_clusters}和图\ref{fig:snare_umap_clusters}展示了基于MultiVI集成结果的聚类分析，验证了算法在细胞类型识别方面的有效性。

\begin{figure}[htbp]
\centering
\includegraphics[width=0.8\textwidth]{images/10x_lymph_node_umap_clusters.png}
\caption{10x\_lymph\_node数据集按聚类结果着色的UMAP可视化}
\label{fig:10x_umap_clusters}
\end{figure}

\begin{figure}[htbp]
\centering
\includegraphics[width=0.8\textwidth]{images/snare_p0_umap_clusters.png}
\caption{snare\_p0数据集按聚类结果着色的UMAP可视化}
\label{fig:snare_umap_clusters}
\end{figure}

\subsection{批次效应校正}

MultiVI在批次效应校正方面的表现如下：

\begin{itemize}
  \item 10x\_lymph\_node: iLISI=0.876, kBET=0.842
  \item snare\_p0: iLISI=0.823, kBET=0.795
  \item paired\_cellline: iLISI=0.912, kBET=0.887
  \item sciCAR\_cellline: iLISI=0.845, kBET=0.821
\end{itemize}

\subsection{算法对比}

与其他算法相比，MultiVI在大多数数据集上表现最佳。表\ref{tab:algorithm_comparison}系统地比较了MultiVI与其他主流算法在聚类准确性、批次效应校正和计算效率方面的性能。

\input{tables/table_2}

从表\ref{tab:algorithm_comparison}可以看出：

1. \textbf{聚类准确性（ARI）}：MultiVI在两个数据集上都取得了最高的ARI分数，分别为0.843（10x\_lymph\_node）和0.921（paired\_cellline），显著优于其他算法。

2. \textbf{批次效应校正（iLISI）}：MultiVI在批次效应校正方面也表现最佳，iLISI分数分别为0.876（10x\_lymph\_node）和0.912（paired\_cellline）。

3. \textbf{计算效率}：虽然MultiVI的计算时间相对较长（38.6分钟和17.8分钟），但考虑到其优异的性能表现，这种时间成本是可以接受的。

为了更直观地展示不同算法的集成效果，图\ref{fig:10x_comparison}和图\ref{fig:snare_comparison}分别展示了各算法在10x\_lymph\_node和snare\_p0数据集上的UMAP可视化比较结果。从图中可以看出，MultiVI在细胞类型分离和批次效应校正方面均表现优异。

\begin{figure}[htbp]
\centering
\includegraphics[width=0.8\textwidth]{images/10x_lymph_node_umap_comparison.png}
\caption{10x\_lymph\_node数据集上不同算法的UMAP可视化比较}
\label{fig:10x_comparison}
\end{figure}

\begin{figure}[htbp]
\centering
\includegraphics[width=0.8\textwidth]{images/snare_p0_umap_comparison.png}
\caption{snare\_p0数据集上不同算法的UMAP可视化比较}
\label{fig:snare_comparison}
\end{figure}

\section{讨论}

\subsection{MultiVI的优势}

\begin{enumerate}
  \item 在细胞类型聚类方面，MultiVI表现最佳，特别是在处理配对数据时。从表\ref{tab:algorithm_comparison}可以看出，MultiVI在ARI指标上显著优于其他算法，从图\ref{fig:10x_umap_celltype}和图\ref{fig:snare_umap_celltype}可以看出，MultiVI能够清晰地分离不同的细胞类型，形成紧密且分离良好的聚类。
  \item 在批次效应校正方面，MultiVI能够有效消除不同来源数据的批次效应。表\ref{tab:multivi_performance}显示MultiVI在所有数据集上都取得了较高的iLISI和kBET分数，图\ref{fig:10x_comparison}和图\ref{fig:snare_comparison}的对比结果显示，MultiVI在保持细胞类型分离的同时，有效地混合了不同批次的细胞。
  \item MultiVI能够处理配对和非配对数据，具有更广泛的适用性。表\ref{tab:datasets_info}展示了不同类型数据集的特征，MultiVI在所有这些数据集上都表现良好。
  \item 从图\ref{fig:10x_metrics}和图\ref{fig:snare_metrics}的分析可以看出，MultiVI在不同分辨率参数下都能保持稳定的性能表现。
\end{enumerate}

\subsection{MultiVI的局限性}

\begin{enumerate}
  \item 计算资源需求较高，特别是在处理大规模数据集时需要GPU加速。
  \item 训练时间较长，相比矩阵分解方法如MOFA+和scAI。
  \item 对超参数较为敏感，需要针对不同数据集进行调优。
\end{enumerate}

% 第五章
\include{chapters/conclusion}

% 添加参考文献
\printbibliography[title=参考文献]

\end{document}









