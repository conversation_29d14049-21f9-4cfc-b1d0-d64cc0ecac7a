{
    "latex-workshop.latex.tools": [
        {
            "name": "xelatex", // 负责中文编译
            "command": "xelatex",
            "args": [
                "-synctex=1",
                "-interaction=nonstopmode",
                "-file-line-error",
                "%DOCFILE%"
            ]
        },
        {
            "name": "biber", // 负责引用文献
            "command": "biber",
            "args": [
                "%DOCFILE%",
            ]
        },
    ],

    "latex-workshop.latex.recipes": [
        {
            "name": "xelatex -> biber -> xelatex",
            "tools": [
                "xelatex",
                "biber",
                "xelatex"
            ]
        },
    ],
    "latex-workshop.latex.autoBuild.run": "never", // 关闭自动编译
    "latex-workshop.showContextMenu": true,
    "latex-workshop.intellisense.package.enabled": true, // 开启自动补全
    "latex-workshop.message.error.show": true, // 开启Error弹窗提示
    "latex-workshop.message.warning.show": false, //关闭Warning弹窗提示
    "latex-workshop.latex.autoClean.run": "onBuilt", // 编译后自动清除中间文件
    "latex-workshop.latex.clean.subfolder.enabled": true, //清除子文件目录中的文件
    "latex-workshop.latex.recipe.default": "lastUsed", // 自动使用上一次的编译器
    "latex-workshop.view.pdf.internal.synctex.keybinding": "double-click",
    "latex-workshop.synctex.afterBuild.enabled": true,
}