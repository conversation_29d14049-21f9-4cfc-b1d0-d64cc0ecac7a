import scvi
import scanpy as sc
import anndata as ad
import numpy as np
import scipy.io
from scipy.sparse import csr_matrix

# 加载数据
# 1. 加载RNA数据
rna_data = scipy.io.mmread("D:/MultiVI/datasets/10x_pbmc/10x_pbmc_rna_normalize_count.mtx")
rna_data = csr_matrix(rna_data)  # 转换为稀疏矩阵

# 2. 加载ATAC数据
atac_data = scipy.io.mmread("D:/MultiVI/datasets/10x_pbmc/10x_pbmc_atac_normalize_count.mtx")
atac_data = csr_matrix(atac_data)  # 转换为稀疏矩阵

# 3. 加载细胞条形码
cell_barcodes = np.loadtxt("D:/MultiVI/datasets/10x_pbmc/10x_pbmc_cell_barcode.txt", dtype=str)

# 4. 加载基因和peak信息
genes = np.loadtxt("D:/MultiVI/datasets/10x_pbmc/10x_pbmc_scale_gene.txt", dtype=str)
peaks = np.loadtxt("D:/MultiVI/datasets/10x_pbmc/10x_pbmc_peak.txt", dtype=str)

# 创建AnnData对象
rna_adata = ad.AnnData(X=rna_data, obs=cell_barcodes, var=genes)
atac_adata = ad.AnnData(X=atac_data, obs=cell_barcodes, var=peaks)

# 合并RNA和ATAC数据
multi_adata = scvi.data.organize_multiome_anndatas(rna_adata, atac_adata)

# 设置MultiVI模型
scvi.model.MULTIVI.setup_anndata(multi_adata, batch_key=None)

# 初始化MultiVI模型
model = scvi.model.MULTIVI(multi_adata, n_latent=30)

# 训练模型
model.train(max_epochs=500, early_stopping=True)

# 保存模型
model.save("D:/MultiVI/datasets/10x_pbmc/multivi_model")

# 获取潜在表示
latent_representation = model.get_latent_representation()

# 保存潜在表示
np.savetxt("D:/MultiVI/datasets/10x_pbmc/multivi_latent_representation.txt", latent_representation)

# 获取重构数据
reconstructed_data = model.get_normalized_expression()

# 保存重构数据
reconstructed_data.to_csv("D:/MultiVI/datasets/10x_pbmc/multivi_reconstructed_data.csv")