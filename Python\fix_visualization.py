#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
修复可视化字体问题的脚本
重新生成批次效应分析的可视化图表，使用英文标题避免字体显示问题
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib
import seaborn as sns
import scanpy as sc

# 设置字体以避免中文显示问题
matplotlib.rcParams['font.family'] = ['DejaVu Sans', 'Arial', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['font.size'] = 12

def fix_visualizations():
    """
    修复可视化图表的字体问题
    """
    print("修复可视化字体问题...")
    
    # 检查是否存在处理后的数据
    data_path = "batch_effect_results/processed_data.h5ad"
    output_dir = "batch_effect_results"
    
    if not os.path.exists(data_path):
        print(f"错误: 找不到处理后的数据文件 {data_path}")
        print("请先运行批次效应分析脚本生成数据")
        return
    
    # 读取数据
    print(f"读取数据: {data_path}")
    adata = sc.read_h5ad(data_path)
    print(f"数据形状: {adata.shape}")
    
    # 检查必要的列是否存在
    required_cols = ['ilisi', 'kbet_pvalue']
    missing_cols = [col for col in required_cols if col not in adata.obs.columns]
    
    if missing_cols:
        print(f"错误: 缺少必要的列: {missing_cols}")
        return
    
    # 重新计算UMAP（如果需要）
    if 'X_umap' not in adata.obsm:
        print("计算UMAP...")
        sc.tl.umap(adata, min_dist=0.3, spread=1.0)
    
    # 1. 创建主要的UMAP图
    print("创建UMAP图...")
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # 按批次着色的UMAP
    sc.pl.umap(adata, color='batch', ax=axes[0,0], show=False, frameon=False,
               title='UMAP - Colored by Batch')
    
    # 按细胞类型着色的UMAP
    sc.pl.umap(adata, color='cell_type', ax=axes[0,1], show=False, frameon=False,
               title='UMAP - Colored by Cell Type')
    
    # iLISI分数分布
    sc.pl.umap(adata, color='ilisi', ax=axes[1,0], show=False, frameon=False,
               cmap='viridis', title='UMAP - iLISI Score')
    
    # kBET p值分布
    sc.pl.umap(adata, color='kbet_pvalue', ax=axes[1,1], show=False, frameon=False,
               cmap='plasma', title='UMAP - kBET p-value')
    
    plt.tight_layout()
    umap_path = os.path.join(output_dir, 'umap_analysis_fixed.png')
    plt.savefig(umap_path, dpi=300, bbox_inches='tight')
    plt.close()
    print(f"UMAP图已保存到: {umap_path}")
    
    # 2. 批次效应指标分布图
    print("创建指标分布图...")
    fig, axes = plt.subplots(2, 2, figsize=(16, 10))
    
    # iLISI分数分布直方图
    axes[0,0].hist(adata.obs['ilisi'], bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    axes[0,0].set_xlabel('iLISI Score')
    axes[0,0].set_ylabel('Frequency')
    axes[0,0].set_title('iLISI Score Distribution')
    mean_ilisi = np.mean(adata.obs['ilisi'])
    axes[0,0].axvline(mean_ilisi, color='red', linestyle='--',
                     label=f'Mean: {mean_ilisi:.3f}')
    axes[0,0].legend()
    
    # kBET p值分布直方图
    axes[0,1].hist(adata.obs['kbet_pvalue'], bins=50, alpha=0.7, color='lightcoral', edgecolor='black')
    axes[0,1].set_xlabel('kBET p-value')
    axes[0,1].set_ylabel('Frequency')
    axes[0,1].set_title('kBET p-value Distribution')
    mean_kbet = np.mean(adata.obs['kbet_pvalue'])
    axes[0,1].axvline(mean_kbet, color='blue', linestyle='--',
                     label=f'Mean: {mean_kbet:.3f}')
    axes[0,1].legend()
    
    # 按批次的iLISI分数箱线图
    batch_ilisi_data = [adata.obs[adata.obs['batch'] == batch]['ilisi'].values
                        for batch in adata.obs['batch'].unique()]
    axes[1,0].boxplot(batch_ilisi_data, labels=adata.obs['batch'].unique())
    axes[1,0].set_xlabel('Batch')
    axes[1,0].set_ylabel('iLISI Score')
    axes[1,0].set_title('iLISI Score by Batch')
    axes[1,0].tick_params(axis='x', rotation=45)
    
    # 按批次的kBET接受率
    batch_kbet_accept = []
    batch_names = []
    for batch in adata.obs['batch'].unique():
        batch_mask = adata.obs['batch'] == batch
        accept_rate = np.mean(adata.obs[batch_mask]['kbet_pvalue'] > 0.05)
        batch_kbet_accept.append(accept_rate)
        batch_names.append(batch)
    
    axes[1,1].bar(batch_names, batch_kbet_accept, alpha=0.7, color='lightgreen', edgecolor='black')
    axes[1,1].set_xlabel('Batch')
    axes[1,1].set_ylabel('kBET Accept Rate')
    axes[1,1].set_title('kBET Accept Rate by Batch')
    axes[1,1].tick_params(axis='x', rotation=45)
    
    # 添加水平线表示总体接受率
    overall_accept_rate = np.mean(adata.obs['kbet_pvalue'] > 0.05)
    axes[1,1].axhline(overall_accept_rate, color='red', linestyle='--',
                     label=f'Overall Rate: {overall_accept_rate:.3f}')
    axes[1,1].legend()
    
    plt.tight_layout()
    metrics_path = os.path.join(output_dir, 'batch_effect_metrics_fixed.png')
    plt.savefig(metrics_path, dpi=300, bbox_inches='tight')
    plt.close()
    print(f"指标分布图已保存到: {metrics_path}")
    
    # 3. 创建批次分布图
    print("创建批次分布图...")
    fig, axes = plt.subplots(1, 2, figsize=(14, 6))
    
    # 批次细胞数量分布
    batch_counts = adata.obs['batch'].value_counts()
    axes[0].bar(batch_counts.index, batch_counts.values, alpha=0.7, color='orange', edgecolor='black')
    axes[0].set_xlabel('Batch')
    axes[0].set_ylabel('Number of Cells')
    axes[0].set_title('Cell Count by Batch')
    axes[0].tick_params(axis='x', rotation=45)
    
    # 添加数值标签
    for i, v in enumerate(batch_counts.values):
        axes[0].text(i, v + max(batch_counts.values) * 0.01, str(v),
                    ha='center', va='bottom', fontweight='bold')
    
    # 细胞类型在各批次中的分布
    batch_celltype_counts = pd.crosstab(adata.obs['batch'], adata.obs['cell_type'])
    batch_celltype_counts.plot(kind='bar', stacked=True, ax=axes[1], alpha=0.8)
    axes[1].set_xlabel('Batch')
    axes[1].set_ylabel('Number of Cells')
    axes[1].set_title('Cell Type Distribution by Batch')
    axes[1].tick_params(axis='x', rotation=45)
    axes[1].legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    
    plt.tight_layout()
    distribution_path = os.path.join(output_dir, 'batch_distribution_fixed.png')
    plt.savefig(distribution_path, dpi=300, bbox_inches='tight')
    plt.close()
    print(f"批次分布图已保存到: {distribution_path}")
    
    # 4. 打印分析结果摘要
    print("\n" + "="*60)
    print("批次效应分析结果摘要")
    print("="*60)
    
    mean_ilisi = np.mean(adata.obs['ilisi'])
    std_ilisi = np.std(adata.obs['ilisi'])
    kbet_accept_rate = np.mean(adata.obs['kbet_pvalue'] > 0.05)
    mean_kbet_pvalue = np.mean(adata.obs['kbet_pvalue'])
    
    print(f"数据概览:")
    print(f"  - 细胞数量: {adata.n_obs:,}")
    print(f"  - 特征数量: {adata.n_vars:,}")
    print(f"  - 分析批次: {', '.join(adata.obs['batch'].unique())}")
    print(f"  - 细胞类型数: {len(adata.obs['cell_type'].unique())}")
    
    print(f"\n批次效应指标:")
    print(f"  - 平均iLISI: {mean_ilisi:.4f} ± {std_ilisi:.4f}")
    print(f"  - kBET接受率: {kbet_accept_rate:.4f}")
    print(f"  - 平均kBET p值: {mean_kbet_pvalue:.4f}")
    
    print(f"\n结果解释:")
    if mean_ilisi > 2.0:
        print(f"  ✅ iLISI分数 ({mean_ilisi:.3f}) 较高，表明批次混合良好")
    else:
        print(f"  ⚠️  iLISI分数 ({mean_ilisi:.3f}) 较低，表明仍存在批次效应")
    
    if kbet_accept_rate > 0.7:
        print(f"  ✅ kBET接受率 ({kbet_accept_rate:.3f}) 较高，表明批次效应较小")
    else:
        print(f"  ⚠️  kBET接受率 ({kbet_accept_rate:.3f}) 较低，表明存在明显批次效应")
    
    print(f"\n修复后的可视化文件:")
    print(f"  - UMAP图: {umap_path}")
    print(f"  - 指标分布图: {metrics_path}")
    print(f"  - 批次分布图: {distribution_path}")

def main():
    """主函数"""
    print("批次效应分析可视化修复工具")
    print("=" * 50)
    
    try:
        fix_visualizations()
        print("\n✅ 可视化修复完成!")
        
    except Exception as e:
        print(f"❌ 错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
