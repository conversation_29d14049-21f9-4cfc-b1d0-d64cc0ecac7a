% 第四章内容 - 修正版
\chapter{实验结果与分析}

\section{实验环境}
本研究的实验环境配置如下：

硬件环境:
\begin{description}
  \item[CPU:] 11th Gen Intel(R) Core(TM) i7-11800H @ 2.30GHz
  \item[GPU:] NVIDIA GeForce RTX 3060 6GB
  \item[RAM:] 32GB DDR4
\end{description}

软件环境:
\begin{description}
  \item[操作系统:] Windows 11 Pro
  \item[Python 版本:] 3.10.9
  \item[主要库版本:] PyTorch 2.0.1, Scanpy 1.9.3, AnnData 0.9.1, scvi-tools 0.20.3
\end{description}

\section{数据集}

本研究使用了四个公开可用的单细胞多组学数据集进行算法评估。表\ref{tab:datasets_info}总结了各数据集的基本信息，包括细胞数量、基因数量、染色质区域数量和数据类型等关键特征。

\input{tables/table_1}

其中，neurips数据集是一个大规模的多批次单细胞多组学数据集，包含69,249个细胞，涵盖13个不同的批次（s1d1至s4d9）和22种细胞类型。该数据集的RNA模态包含13,431个基因，ATAC模态包含116,490个染色质可及性峰。neurips数据集的批次来源于不同的实验条件：不同供体（s1-s4）、不同时间点（d1-d10），为批次效应分析提供了丰富的实验设计。

\section{评估方法}
本研究采用了以下实验方法来评估MultiVI算法的性能：首先进行数据预处理，对原始数据进行质量控制、标准化和特征选择；然后进行模型训练，使用不同的超参数配置训练MultiVI模型；接着进行性能评估，使用多种指标评估模型在细胞聚类和批次效应校正方面的表现；最后进行算法对比，与其他主流算法进行对比分析。

\section{实验结果}

\subsection{模型训练与收敛}

MultiVI模型在不同数据集上的训练时间和收敛情况如下：10x\_lymph\_node的训练时间为26.8分钟，收敛轮数为315，最终验证损失为1.642；snare\_p0的训练时间为15.2分钟，收敛轮数为298，最终验证损失为2.047；paired\_cellline的训练时间为5.7分钟，收敛轮数为245，最终验证损失为1.523。

本研究发现，模型训练在RTX 3060 GPU上能够高效完成，相比CPU训练提高了显著的速度。通过第三章介绍的参数优化方法，本研究确定了各数据集的最优聚类分辨率参数。

\subsection{细胞聚类与可视化分析}

MultiVI在不同数据集上的聚类性能评估结果如下：10x\_lymph\_node的ARI为0.854，NMI为0.895，V-measure为0.871；snare\_p0的ARI为0.756，NMI为0.800，V-measure为0.765；paired\_cellline的ARI为0.921，NMI为0.945，V-measure为0.937。

表\ref{tab:multivi_performance}详细展示了MultiVI在三个数据集上的聚类性能和训练指标评估结果。

\input{tables/table_3}

MultiVI在细胞类型聚类方面表现优异，能够准确识别和分离不同的细胞类型。无监督聚类结果与已知的细胞类型标注高度一致，验证了算法在细胞类型识别方面的准确性。

\subsection{批次效应校正}

由于10x\_lymph\_node、snare\_p0和paired\_cellline数据集在原理上不适合进行批次效应评估（缺乏真实的批次变异），本研究使用neurips数据集进行批次效应校正的专门评估。neurips数据集包含来自不同实验条件和时间点的多个批次，为批次效应分析提供了理想的测试环境。

本研究选择了三个具有代表性的批次组合进行分析：

\textbf{批次组合1（s1d1\_s1d2\_s1d3）}：来自同一供体不同时间点的批次，包含17,243个细胞。MultiVI在该组合上的批次效应校正表现为：平均iLISI分数为1.917，kBET接受率为0.324。这表明MultiVI能够有效整合来自同一供体不同时间点的数据，实现良好的批次混合。图\ref{fig:batch_s1d1_s1d2_s1d3}展示了该批次组合的UMAP聚类结果。

\begin{figure}[htbp]
\centering
\includegraphics[width=0.8\textwidth]{images/batch_s1d1_s1d2_s1d3_umap.png}
\caption{批次组合s1d1\_s1d2\_s1d3的UMAP聚类可视化}
\label{fig:batch_s1d1_s1d2_s1d3}
\end{figure}

\textbf{批次组合2（s1d1\_s2d1\_s4d1）}：来自不同供体同一时间点的批次，包含18,467个细胞。该组合的批次效应校正结果为：平均iLISI分数为1.276，kBET接受率为0.025。相对较低的指标值反映了不同供体间的生物学差异，这种差异在一定程度上应当被保留。图\ref{fig:batch_s1d1_s2d1_s4d1}展示了该批次组合的UMAP聚类结果。

\begin{figure}[htbp]
\centering
\includegraphics[width=0.8\textwidth]{images/batch_s1d1_s2d1_s4d1_umap.png}
\caption{批次组合s1d1\_s2d1\_s4d1的UMAP聚类可视化}
\label{fig:batch_s1d1_s2d1_s4d1}
\end{figure}

\textbf{批次组合3（s4d1\_s4d8\_s4d9）}：来自同一供体不同发育阶段的批次，包含22,224个细胞。MultiVI在该组合上取得了良好的批次校正效果：平均iLISI分数为1.874，kBET接受率为0.341。这表明算法能够有效处理发育时间序列中的批次效应。图\ref{fig:batch_s4d1_s4d8_s4d9}展示了该批次组合的UMAP聚类结果。

\begin{figure}[htbp]
\centering
\includegraphics[width=0.8\textwidth]{images/batch_s4d1_s4d8_s4d9_umap.png}
\caption{批次组合s4d1\_s4d8\_s4d9的UMAP聚类可视化}
\label{fig:batch_s4d1_s4d8_s4d9}
\end{figure}

表\ref{tab:batch_effect}总结了MultiVI在neurips数据集不同批次组合上的批次效应校正性能。

\input{tables/table_batch_effect}

从图\ref{fig:batch_s1d1_s1d2_s1d3}、图\ref{fig:batch_s1d1_s2d1_s4d1}和图\ref{fig:batch_s4d1_s4d8_s4d9}的UMAP可视化结果可以看出，MultiVI能够有效地将不同批次的细胞进行整合，同时保持细胞类型的清晰分离。特别是在同供体不同时间点的批次组合中，不同批次的细胞实现了良好的混合，表明算法成功消除了技术性批次效应。

总体而言，MultiVI在neurips数据集上展现出良好的批次效应校正能力，特别是在处理技术性批次效应方面表现优异，同时能够适当保留生物学相关的批次间差异。

\subsection{算法对比}

与其他算法相比，MultiVI在大多数数据集上表现最佳。表\ref{tab:algorithm_comparison}系统地比较了MultiVI与其他主流算法在聚类准确性和计算效率方面的性能。

\input{tables/table_2}

从表\ref{tab:algorithm_comparison}可以看出：在聚类准确性方面，MultiVI在两个数据集上都取得了最高的ARI分数，分别为0.854（10x\_lymph\_node）和0.921（paired\_cellline），显著优于其他算法；在计算效率方面，MultiVI在不同规模数据集上都表现出良好的效率，paired\_cellline数据集训练时间为5.7分钟，10x\_lymph\_node数据集训练时间为26.8分钟，考虑到其优异的性能表现，这种时间成本是可以接受的。

MultiVI在细胞类型分离方面表现优异。

\section{讨论}

\subsection{MultiVI的优势}

首先，在细胞类型聚类方面，MultiVI表现最佳，特别是在处理配对数据时。从表\ref{tab:algorithm_comparison}可以看出，MultiVI在ARI指标上显著优于其他算法，能够清晰地分离不同的细胞类型，形成紧密且分离良好的聚类。其次，在批次效应校正方面，MultiVI在neurips数据集上展现出良好的性能。表\ref{tab:batch_effect}显示，MultiVI能够有效处理不同类型的批次效应，特别是在同供体不同时间点和同供体不同发育阶段的场景下表现优异。再次，在参数优化方面，图\ref{fig:10x_metrics}、图\ref{fig:paired_cellline_metrics}和图\ref{fig:snare_metrics}展示了MultiVI在不同数据集上的参数敏感性分析，证明了算法在各种分辨率参数下的稳定性。最后，MultiVI能够处理配对和非配对数据，具有更广泛的适用性，在计算效率方面也表现出色。

\subsection{MultiVI的局限性}

MultiVI也存在一些局限性：计算资源需求较高，特别是在处理大规模数据集时需要GPU加速；训练时间较长，相比矩阵分解方法如MOFA+和scAI；对超参数较为敏感，需要针对不同数据集进行调优。