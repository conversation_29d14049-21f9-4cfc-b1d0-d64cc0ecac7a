from scipy.io import mmread
import scipy.sparse
import anndata as ad
import pandas as pd
import scvi
import numpy as np
import scanpy as sc
import os
import time
import torch

# 设置随机种子
scvi.settings.seed = 420

### 用户可修改参数区域 ##################################################
# 数据集前缀，修改此变量即可适应不同数据集
dataset_prefix = "10x_lymph_node"
# 数据存放目录路径（基于数据集前缀）
data_path = f"../datasets/{dataset_prefix}/"
# 输出目录（基于数据集前缀）
output_dir = f"D:/MultiVI/output/{dataset_prefix}/"
min_rna_genes = 20  # RNA最小表达细胞数
min_atac_cells = 10  # ATAC最小出现细胞数
training_epochs = 500  # 训练轮数
# 元数据中记录细胞类型的列名
cell_type_column = "labels"
#######################################################################

def load_modality(modality):
    """加载指定模态的数据"""
    if modality == "rna":
        count_path = os.path.join(data_path, f"{dataset_prefix}_rna_normalize_count.mtx")
        features_path = os.path.join(data_path, f"{dataset_prefix}_scale_gene.txt")
        barcodes_path = os.path.join(data_path, f"{dataset_prefix}_cell_barcode.txt")
    elif modality == "atac":
        count_path = os.path.join(data_path, f"{dataset_prefix}_atac_normalize_count.mtx")
        features_path = os.path.join(data_path, f"{dataset_prefix}_peak.txt")
        barcodes_path = os.path.join(data_path, f"{dataset_prefix}_cell_barcode.txt")
    
    # 检查文件是否存在
    for path in [count_path, features_path, barcodes_path]:
        if not os.path.exists(path):
            raise FileNotFoundError(path)
    
    # 加载数据
    X = mmread(count_path).T
    # 确保X是CSR格式，这样可以支持索引操作
    if isinstance(X, scipy.sparse.coo_matrix):
        X = X.tocsr()
    
    barcodes = pd.read_csv(barcodes_path, sep='\t', header=None)[0].values
    features = pd.read_csv(features_path, sep='\t', header=None)[0].values
    
    # 创建AnnData对象
    adata = ad.AnnData(
        X=X,
        obs=pd.DataFrame(index=barcodes),
        var=pd.DataFrame(index=features),
    )

    return adata


def load_metadata():
    """加载细胞元数据（如果存在）"""
    metadata_path = os.path.join(data_path, f"{dataset_prefix}_wnn_output.txt")
    if os.path.exists(metadata_path):
        metadata = pd.read_csv(metadata_path, sep="\t", index_col=0)
        return metadata
    return None


### 主流程 ##########################################################
if __name__ == "__main__":
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    try:
        print("\n" + "=" * 50)
        print(f"Starting MultiVI processing for {dataset_prefix}")
        print(f"Data location: {data_path}")
        print("=" * 50 + "\n")

        # 加载数据
        print("[1/7] Loading RNA data...")
        adata_rna = load_modality("rna")
        if isinstance(adata_rna.X, scipy.sparse.coo_matrix):
            adata_rna.X = adata_rna.X.tocsr()
        print(f"RNA: {adata_rna.n_obs} cells, {adata_rna.n_vars} genes")

        # RNA数据预处理
        sc.pp.filter_cells(adata_rna, min_genes=10)
        sc.pp.filter_genes(adata_rna, min_cells=min_rna_genes)

        print("[2/7] Loading ATAC data...")
        adata_atac = load_modality("atac")
        if isinstance(adata_atac.X, scipy.sparse.coo_matrix):
            adata_atac.X = adata_atac.X.tocsr()
        print(f"ATAC: {adata_atac.n_obs} cells, {adata_atac.n_vars} peaks")

        # ATAC数据预处理
        sc.pp.filter_cells(adata_atac, min_genes=10)
        sc.pp.filter_genes(adata_atac, min_cells=min_atac_cells)

        # 加载元数据
        print("[3/7] Loading metadata...")
        metadata = load_metadata()
        if metadata is not None:
            print(f"Metadata loaded with {metadata.shape[0]} cells and {metadata.shape[1]} attributes")
            # 添加细胞类型信息到obs
            if cell_type_column in metadata.columns:
                adata_rna.obs["cell_type"] = metadata.loc[adata_rna.obs_names, cell_type_column]
                adata_atac.obs["cell_type"] = metadata.loc[adata_atac.obs_names, cell_type_column]
                print(f"Cell type information added from '{cell_type_column}' column")
            else:
                print(f"Warning: Cell type column '{cell_type_column}' not found in metadata")
                print(f"Available columns: {', '.join(metadata.columns)}")

        # 细胞对齐
        print("[4/7] Aligning cells...")
        common_cells = adata_rna.obs_names.intersection(adata_atac.obs_names)
        print(f"Found {len(common_cells)} common cells")

        adata_rna = adata_rna[common_cells].copy()
        adata_atac = adata_atac[common_cells].copy()

        # 合并数据
        print("[5/7] Merging data...")
        adata_paired = ad.concat([adata_rna, adata_atac], merge="same", axis=1)
        adata_paired.var['modality'] = ['Gene Expression'] * adata_rna.shape[1] + ['Peaks'] * adata_atac.shape[1]

        # 使用scvi的organize_multiome_anndatas方法组织数据
        print("[6/7] Organizing multiome data...")
        adata_mvi = scvi.data.organize_multiome_anndatas(adata_paired)
        del adata_paired  # 释放内存

        # 按模态排序变量
        adata_mvi = adata_mvi[:, adata_mvi.var["modality"].argsort()].copy()

        # 设置MultiVI模型
        scvi.model.MULTIVI.setup_anndata(adata_mvi, batch_key="modality")

        # 初始化模型
        print("Initializing MultiVI model...")
        model = scvi.model.MULTIVI(
            adata_mvi,
            n_genes=(adata_mvi.var["modality"] == "Gene Expression").sum(),
            n_regions=(adata_mvi.var["modality"] == "Peaks").sum(),
        )

        # 查看模型设置
        model.view_anndata_setup()

        # 训练模型
        print("[7/7] Training model...")
        start_time = time.time()

        # 使用GPU加速训练
        if torch.cuda.is_available():
            print(f"GPU设备: {torch.cuda.get_device_name(0)}")
            print(f"CUDA版本: {torch.version.cuda}")

        model.train(
            max_epochs=training_epochs,
            early_stopping=True,
            check_val_every_n_epoch=10,
            plan_kwargs={"lr": 2e-4}
        )

        training_time = (time.time() - start_time) / 60
        print(f"Training completed in {training_time:.1f} minutes")

        # 保存模型
        model_save_path = os.path.join(output_dir, f"{dataset_prefix}_model")
        model.save(model_save_path)
        print(f"Model saved to {model_save_path}")

        # 获取潜在表示
        latent = model.get_latent_representation()

        # 保存潜在表示
        latent_save_path = os.path.join(output_dir, f"{dataset_prefix}_latent.csv")
        np.savetxt(latent_save_path, latent, delimiter=',')
        print(f"Latent representation saved to {latent_save_path}")

        # 添加潜在表示到AnnData对象
        adata_mvi.obsm["X_multivi"] = latent

        # 计算邻居图和UMAP
        sc.pp.neighbors(adata_mvi, use_rep="X_multivi", n_neighbors=30)
        sc.tl.umap(adata_mvi)

        # 如果有细胞类型信息，进行评估
        if "cell_type" in adata_mvi.obs:
            print("\nEvaluating clustering against known cell types...")
            # 使用MultiVI潜在表示进行聚类
            sc.tl.leiden(adata_mvi, resolution=0.8)

            # 计算聚类与真实细胞类型的一致性
            from sklearn.metrics import adjusted_rand_score

            ari = adjusted_rand_score(adata_mvi.obs["cell_type"], adata_mvi.obs["leiden"])
            print(f"Adjusted Rand Index: {ari:.4f}")

            # 保存带有聚类结果的AnnData对象
            adata_save_path = os.path.join(output_dir, f"{dataset_prefix}_adata.h5ad")
            adata_mvi.write(adata_save_path)
            print(f"Annotated AnnData saved to {adata_save_path}")

        print("\n" + "=" * 50)
        print(f"Processing completed successfully for {dataset_prefix}")
        print("=" * 50)

    except FileNotFoundError as e:
        print(f"\nError: Missing file - {e.filename}")
    except Exception as e:
        print(f"\nUnexpected error: {str(e)}")
        import traceback

        traceback.print_exc()
