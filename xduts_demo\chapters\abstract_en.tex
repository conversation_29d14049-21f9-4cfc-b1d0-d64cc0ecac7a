Single-cell multi-omics sequencing technologies can simultaneously measure multiple molecular features such as gene expression and chromatin accessibility in the same cell, providing powerful tools for in-depth study of cellular heterogeneity and regulatory networks. However, these data present challenges including high dimensionality, sparsity, modal heterogeneity, and batch effects. Particularly, datasets often contain a mixture of paired (multi-modal) and unpaired (single-modal) cells, requiring specialized methods for effective integration. \textbf{MultiVI} is an advanced method based on deep generative models (variational autoencoders) that can integrate paired and unpaired single-cell data, construct a joint latent representation space, and infer missing modalities. This thesis aims to deeply understand the principles of MultiVI and implement it, while comparing and evaluating it with other mainstream integration algorithms (such as scAI, MOFA+, scMVP, Seurat). The research is based on the Python environment, implementing the core algorithm of MultiVI, and conducting preliminary tests on selected single-cell multi-omics datasets with good integration results. Subsequently, comparative algorithm implementation will be completed, and comprehensive evaluation of the performance of various algorithms in cell type clustering, batch correction, and biological information retention will be conducted using metrics such as ARI, NMI, and cASW. This thesis will provide implementation references and comparative analysis for single-cell multi-omics data integration, which has important scientific and application value.

\textbf{Keywords}: Single-cell Multi-omics, Data Integration, MultiVI, Deep Learning
