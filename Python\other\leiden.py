import mudata as md
import numpy as np
import scanpy as sc
from sklearn.metrics import adjusted_rand_score, normalized_mutual_info_score
from sklearn.metrics import adjusted_mutual_info_score
from sklearn.metrics import v_measure_score
from sklearn.metrics import mutual_info_score
from sklearn.metrics import homogeneity_score

adata = sc.read_h5ad("ATAC_unpred.h5ad")
 # 计算高维空间中的k近邻图
sc.pp.neighbors(adata, use_rep="X")  # 修改：neighbors_key为"neighbors"
#
# 设置分辨率范围
resolution_range = np.arange(0, 3, 0.1)

# 初始化最大值
max_ari = -float('inf')
max_nmi = -float('inf')
max_ami = -float('inf')
max_v_measure = -float('inf')
max_mutual_info = -float('inf')
max_homogeneity = -float('inf')
best_resolution = None

# 循环遍历不同的分辨率
for i in resolution_range:
    try:
        # 进行Leiden聚类
        sc.tl.leiden(adata, key_added="leiden_mofa", resolution=i)

        # 计算聚类指标
        ari = adjusted_rand_score(adata.obs["cell_type"], adata.obs["leiden_mofa"])
        nmi = normalized_mutual_info_score(adata.obs["cell_type"], adata.obs["leiden_mofa"])
        ami = adjusted_mutual_info_score(adata.obs["cell_type"], adata.obs["leiden_mofa"])
        v_measure = v_measure_score(adata.obs["cell_type"], adata.obs["leiden_mofa"])
        mutual_info = mutual_info_score(adata.obs["cell_type"], adata.obs["leiden_mofa"])
        homogeneity = homogeneity_score(adata.obs["cell_type"], adata.obs["leiden_mofa"])

        # 更新最大值
        if ari > max_ari:
            max_ari = ari
            best_resolution = i
        if nmi > max_nmi:
            max_nmi = nmi
        if ami > max_ami:
            max_ami = ami
        if v_measure > max_v_measure:
            max_v_measure = v_measure
        if mutual_info > max_mutual_info:
            max_mutual_info = mutual_info
        if homogeneity > max_homogeneity:
            max_homogeneity = homogeneity

    except Exception as e:
        print(f"在分辨率 {i} 时聚类时出错: {e}")
        continue

print(f"Adjusted Rand Index (ARI): {max_ari}")
print(f"Normalized Mutual Information (NMI): {max_nmi}")
print(f"Adjusted Mutual Information (AMI): {max_ami}")
print(f"V-Measure: {max_v_measure}")
print(f"Mutual Information: {max_mutual_info}")
print(f"Homogeneity: {max_homogeneity}")

# 进行PCA
sc.tl.pca(adata, n_comps=50)

# 使用PCA的结果进行t-SNE
sc.tl.tsne(adata, use_rep="X_pca")

# 绘制tSNE降维图
sc.pl.tsne(adata, color=["cell_type", "leiden_mofa"], show=True)

