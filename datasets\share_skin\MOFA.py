import numpy as np
import pandas as pd
import scanpy as sc
import anndata as ad
from scipy.io import mmread
from scipy.sparse import csr_matrix
import os
import time
import torch
import torch.nn as nn
import torch.optim as optim

# 数据路径和输出文件名
data_directory = "D:/MultiVI/datasets/share_skin"
output_h5ad_file = os.path.join(data_directory, "share_skin_MOFA.h5ad")

# 检查输入路径是否存在
if not os.path.exists(data_directory):
    print("Input Data does not exist.")
    exit(1)

# 打印解析结果
print("Input file:", data_directory)
print("Output file:", output_h5ad_file)

# RNA 数据路径
rna_count_path = os.path.join(data_directory, "share_skin_rna_normalize_count.mtx")
rna_genes_path = os.path.join(data_directory, "share_skin_scale_gene.txt")
rna_barcodes_path = os.path.join(data_directory, "share_skin_cell_barcode.txt")

# ATAC 数据路径
atac_count_path = os.path.join(data_directory, "share_skin_atac_normalize_count.mtx")
atac_peaks_path = os.path.join(data_directory, "share_skin_peak.txt")
atac_barcodes_path = os.path.join(data_directory, "share_skin_cell_barcode.txt")

# 加载 RNA 数据
cell_names = pd.read_csv(rna_barcodes_path, sep='\t', header=None, index_col=None)
cell_names.columns = ['cell_ids']
X_rna = csr_matrix(mmread(rna_count_path).T)
gene_names = pd.read_csv(rna_genes_path, sep='\t', header=None, index_col=None)
gene_names.columns = ['gene_ids']

# 创建 RNA 的 AnnData 对象
adata_rna = ad.AnnData(
    X=X_rna,
    obs=pd.DataFrame(index=cell_names.cell_ids),
    var=pd.DataFrame(index=gene_names.gene_ids),
    dtype=X_rna.dtype
)
adata_rna.var_names_make_unique()

# RNA 数据预处理
sc.pp.filter_cells(adata_rna, min_genes=10)
sc.pp.filter_genes(adata_rna, min_cells=20)
sc.pp.highly_variable_genes(adata_rna, flavor="seurat_v3", n_top_genes=4000, subset=True)
sc.pp.normalize_total(adata_rna, target_sum=1e4)
sc.pp.log1p(adata_rna)

# 加载 ATAC 数据
cell_names = pd.read_csv(atac_barcodes_path, sep='\t', header=None, index_col=None)
cell_names.columns = ['cell_ids']
X_atac = csr_matrix(mmread(atac_count_path).T)
peak_names = pd.read_csv(atac_peaks_path, sep='\t', header=None, index_col=None)
peak_names.columns = ['peak_ids']

# 创建 ATAC 的 AnnData 对象
adata_atac = ad.AnnData(
    X=X_atac,
    obs=pd.DataFrame(index=cell_names.cell_ids),
    var=pd.DataFrame(index=peak_names.peak_ids),
    dtype=X_atac.dtype
)

# ATAC 数据预处理
sc.pp.filter_cells(adata_atac, min_genes=10)
sc.pp.filter_genes(adata_atac, min_cells=1)
sc.pp.highly_variable_genes(adata_atac, flavor="seurat_v3", n_top_genes=30000, subset=True)
sc.pp.normalize_total(adata_atac, target_sum=1e4)
sc.pp.log1p(adata_atac)

# 对齐样本
common_cells = set(adata_rna.obs.index) & set(adata_atac.obs.index)
adata_rna = adata_rna[list(common_cells)]
adata_atac = adata_atac[list(common_cells)]

# 检查样本数
print("RNA samples after alignment:", adata_rna.n_obs)
print("ATAC samples after alignment:", adata_atac.n_obs)

# 将数据转换为 PyTorch 张量
X_rna_tensor = torch.tensor(adata_rna.X.toarray(), dtype=torch.float32)
X_atac_tensor = torch.tensor(adata_atac.X.toarray(), dtype=torch.float32)

# 定义 MOFA 模型
class MOFA(nn.Module):
    def __init__(self, n_samples, n_factors, n_features_rna, n_features_atac):
        super(MOFA, self).__init__()
        self.Z = nn.Parameter(torch.randn(n_samples, n_factors))  # 潜在因子
        self.W_rna = nn.Parameter(torch.randn(n_factors, n_features_rna))  # RNA 权重矩阵
        self.W_atac = nn.Parameter(torch.randn(n_factors, n_features_atac))  # ATAC 权重矩阵

    def forward(self):
        X_rna_reconstructed = torch.matmul(self.Z, self.W_rna)
        X_atac_reconstructed = torch.matmul(self.Z, self.W_atac)
        return X_rna_reconstructed, X_atac_reconstructed

# 初始化模型
n_samples = adata_rna.n_obs  # 使用 RNA 数据的样本数
n_features_rna = X_rna_tensor.shape[1]
n_features_atac = X_atac_tensor.shape[1]
n_factors = 15  # 潜在因子数量
model = MOFA(n_samples, n_factors, n_features_rna, n_features_atac)

# 使用 GPU
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
model.to(device)
X_rna_tensor = X_rna_tensor.to(device)
X_atac_tensor = X_atac_tensor.to(device)

# 定义损失函数和优化器
criterion = nn.MSELoss()
optimizer = optim.Adam(model.parameters(), lr=0.01)

# 训练模型
epochs = 100
start_time = time.time()
for epoch in range(epochs):
    optimizer.zero_grad()
    X_rna_reconstructed, X_atac_reconstructed = model()
    loss_rna = criterion(X_rna_reconstructed, X_rna_tensor)
    loss_atac = criterion(X_atac_reconstructed, X_atac_tensor)
    loss = loss_rna + loss_atac
    loss.backward()
    optimizer.step()
    print(f"Epoch {epoch+1}/{epochs}, Loss: {loss.item()}")

# 获取潜在因子
with torch.no_grad():
    Z = model.Z.cpu().numpy()

# 将潜在因子添加到 AnnData 对象
adata_rna.obsm['X_mofa'] = Z  # 使用 RNA 的 AnnData 对象

# 保存为 .h5ad 文件
adata_rna.write(output_h5ad_file)
print(f"Results saved to {output_h5ad_file}")

# 打印运行时间
end_time = time.time()
execution_time = end_time - start_time
print(f"Running time: {execution_time:.2f} seconds")
