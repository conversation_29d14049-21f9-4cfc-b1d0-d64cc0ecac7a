
批次效应分析报告 (CUDA加速版本)
================================

数据概览:
--------
- 总细胞数: 22,224
- 总特征数: 13,181
- 分析批次: s4d1, s4d8, s4d9
- 细胞类型数: 20

批次效应指标:
-----------
iLISI (integrated Local Inverse Simpson Index):
- 平均值: 1.8737
- 标准差: 0.5514
- 中位数: 1.8672
- 解释: iLISI值越高表示批次混合越好，理想值接近批次数量(3)

kBET (k-nearest neighbor Batch Effect Test):
- 接受率: 0.3411
- 平均p值: 0.1119
- 中位数p值: 0.0096
- 解释: 接受率越高表示批次效应越小，理想值接近1.0

各批次详细统计:
--------------

批次 s4d1:
  - 细胞数: 8,023
  - 平均iLISI: 1.8901 ± 0.4978
  - kBET接受率: 0.3314
  - 平均kBET p值: 0.1104

批次 s4d8:
  - 细胞数: 9,876
  - 平均iLISI: 1.7607 ± 0.5707
  - kBET接受率: 0.3862
  - 平均kBET p值: 0.1248

批次 s4d9:
  - 细胞数: 4,325
  - 平均iLISI: 2.1015 ± 0.5267
  - kBET接受率: 0.2562
  - 平均kBET p值: 0.0851

结论:
----
基于iLISI和kBET指标的分析结果:
- iLISI平均分数为1.8737，表明仍存在一定批次效应
- kBET接受率为0.3411，表明存在明显批次效应

建议:
----
建议进一步进行批次效应校正处理。

技术说明:
--------
- 本分析使用CUDA加速计算，提高了处理效率
- 为优化性能，选择了高变特征进行分析
- 使用分批处理策略以节省内存
