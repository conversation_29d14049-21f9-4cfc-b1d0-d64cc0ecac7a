#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MultiVI结果分析模块
用于分析MultiVI模型训练完成后的结果，包括聚类评估、可视化和结果保存
"""

import os
import time
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import scanpy as sc
import anndata as ad
import scvi
from sklearn.metrics import (
    adjusted_rand_score, 
    normalized_mutual_info_score,
    adjusted_mutual_info_score,
    v_measure_score,
    mutual_info_score,
    homogeneity_score
)

# 用户可修改的参数 #################################################
MODEL_PATH = "D:/MultiVI/output/paired_cellline/paired_cellline_model"  # 模型路径
OUTPUT_PATH = "D:/MultiVI/output/paired_cellline/analysis"  # 输出目录
PREFIX = "paired_cellline"  # 输出文件前缀
ADATA_PATH = "D:/MultiVI/output/paired_cellline/paired_cellline_adata.h5ad"  # 可选的AnnData文件路径，如果为None则从模型加载
CELL_TYPE_KEY = "cell_type"  # 细胞类型的键名
#################################################################


def load_model_and_data(model_path, adata_path=None):
    """
    加载MultiVI模型和数据
    
    参数:
        model_path: 模型保存路径
        adata_path: 可选，AnnData文件路径。如果提供，则从文件加载数据
        
    返回:
        model: 加载的MultiVI模型
        adata: 加载的AnnData对象
    """
    print(f"加载模型: {model_path}")
    
    if adata_path and os.path.exists(adata_path):
        print(f"从文件加载数据: {adata_path}")
        adata = sc.read_h5ad(adata_path)
        model = scvi.model.MULTIVI.load(model_path, adata=adata)
    else:
        print("从模型加载数据")
        model = scvi.model.MULTIVI.load(model_path)
        adata = model.adata
    
    print(f"数据加载完成: {adata.n_obs} 细胞, {adata.n_vars} 特征")
    return model, adata


def extract_latent_representation(model, output_path, prefix):
    """
    提取并保存潜在表示
    
    参数:
        model: MultiVI模型
        output_path: 输出目录
        prefix: 输出文件前缀
        
    返回:
        latent: 潜在表示矩阵
    """
    print("提取潜在表示...")
    latent = model.get_latent_representation()
    
    # 保存潜在表示
    latent_save_path = os.path.join(output_path, f"{prefix}_latent.csv")
    np.savetxt(latent_save_path, latent, delimiter=',')
    print(f"潜在表示已保存到: {latent_save_path}")
    
    return latent


def find_optimal_resolution(adata, latent_key, cell_type_key, output_path, prefix):
    """
    寻找最佳聚类分辨率
    
    参数:
        adata: AnnData对象
        latent_key: 潜在表示的键名
        cell_type_key: 细胞类型的键名
        output_path: 输出目录
        prefix: 输出文件前缀
        
    返回:
        best_resolution: 最佳分辨率
        metrics_df: 包含所有分辨率评估指标的DataFrame
    """
    print("\n寻找最佳聚类分辨率...")
    
    # 计算邻居图
    neighbors_key = f"{latent_key}_neighbors"
    sc.pp.neighbors(adata, use_rep=latent_key, n_neighbors=30, key_added=neighbors_key)
    
    # 设置分辨率范围
    resolution_range = np.arange(0, 3, 0.1)
    
    # 初始化最大值和最佳分辨率
    max_ari = -float('inf')
    best_resolution = None
    
    # 创建结果表格
    results = []
    
    # 循环遍历不同的分辨率
    for res in resolution_range:
        try:
            # 进行Leiden聚类
            leiden_key = f"leiden_{res:.1f}"
            sc.tl.leiden(adata, neighbors_key=neighbors_key, key_added=leiden_key, resolution=res)
            
            # 计算聚类指标
            ari = adjusted_rand_score(adata.obs[cell_type_key], adata.obs[leiden_key])
            nmi = normalized_mutual_info_score(adata.obs[cell_type_key], adata.obs[leiden_key])
            ami = adjusted_mutual_info_score(adata.obs[cell_type_key], adata.obs[leiden_key])
            v_measure = v_measure_score(adata.obs[cell_type_key], adata.obs[leiden_key])
            mutual_info = mutual_info_score(adata.obs[cell_type_key], adata.obs[leiden_key])
            homogeneity = homogeneity_score(adata.obs[cell_type_key], adata.obs[leiden_key])
            
            # 保存结果
            results.append({
                "resolution": res,
                "ARI": ari,
                "NMI": nmi,
                "AMI": ami,
                "V-Measure": v_measure,
                "Mutual Info": mutual_info,
                "Homogeneity": homogeneity
            })
            
            # 更新最大值
            if ari > max_ari:
                max_ari = ari
                best_resolution = res
            
        except Exception as e:
            print(f"在分辨率 {res} 时聚类出错: {e}")
            continue
    
    # 创建DataFrame并保存
    metrics_df = pd.DataFrame(results)
    metrics_path = os.path.join(output_path, f"{prefix}_clustering_metrics.csv")
    metrics_df.to_csv(metrics_path, index=False)
    print(f"聚类评估指标已保存到: {metrics_path}")
    
    # 打印最佳结果
    print(f"\n最佳分辨率 = {best_resolution}")
    print(f"Max ARI = {max_ari:.4f}")
    
    # 使用最佳分辨率进行最终聚类
    sc.tl.leiden(adata, neighbors_key=neighbors_key, key_added="leiden", resolution=best_resolution)
    
    return best_resolution, metrics_df


def plot_metrics_vs_resolution(metrics_df, best_resolution, output_path, prefix):
    """
    绘制聚类指标随分辨率变化的曲线
    
    参数:
        metrics_df: 包含所有分辨率评估指标的DataFrame
        best_resolution: 最佳分辨率
        output_path: 输出目录
        prefix: 输出文件前缀
    """
    plt.figure(figsize=(12, 8))
    metrics = ["ARI", "NMI", "AMI", "V-Measure", "Homogeneity"]
    for metric in metrics:
        plt.plot(metrics_df["resolution"], metrics_df[metric], marker='o', label=metric)
    
    plt.axvline(x=best_resolution, color='r', linestyle='--', label=f'Best Resolution: {best_resolution}')
    plt.xlabel('Resolution')
    plt.ylabel('Score')
    plt.title('Clustering Metrics vs Resolution')
    plt.legend()
    plt.grid(True)
    plt.tight_layout()
    
    metrics_plot_path = os.path.join(output_path, f"{prefix}_metrics_vs_resolution.png")
    plt.savefig(metrics_plot_path, dpi=300)
    print(f"指标曲线已保存到: {metrics_plot_path}")


def generate_visualizations(adata, cell_type_key, output_path, prefix):
    """
    生成可视化结果
    
    参数:
        adata: AnnData对象
        cell_type_key: 细胞类型的键名
        output_path: 输出目录
        prefix: 输出文件前缀
    """
    print("\n生成可视化结果...")
    
    # 计算UMAP
    if "X_umap" not in adata.obsm:
        print("计算UMAP投影...")
        sc.tl.umap(adata)
    
    # 可视化聚类结果
    clusters_plot_path = os.path.join(output_path, f"{prefix}_umap_clusters.png")
    fig = sc.pl.umap(adata, color=["leiden"], return_fig=True)
    fig.savefig(clusters_plot_path, dpi=300, bbox_inches="tight")
    print(f"聚类UMAP可视化已保存到: {clusters_plot_path}")
    
    # 可视化真实细胞类型
    celltype_plot_path = os.path.join(output_path, f"{prefix}_umap_celltype.png")
    fig = sc.pl.umap(adata, color=[cell_type_key], return_fig=True)
    fig.savefig(celltype_plot_path, dpi=300, bbox_inches="tight")
    print(f"细胞类型UMAP可视化已保存到: {celltype_plot_path}")
    
    # 创建聚类结果与真实细胞类型的对比图
    comparison_plot_path = os.path.join(output_path, f"{prefix}_umap_comparison.png")
    fig, axs = plt.subplots(1, 2, figsize=(16, 7))
    sc.pl.umap(adata, color="leiden", title="MultiVI Clusters", ax=axs[0], show=False)
    sc.pl.umap(adata, color=cell_type_key, title="True Cell Types", ax=axs[1], show=False)
    plt.tight_layout()
    fig.savefig(comparison_plot_path, dpi=300, bbox_inches="tight")
    print(f"聚类与真实细胞类型对比图已保存到: {comparison_plot_path}")


def analyze_multivi_results():
    """
    分析MultiVI模型结果的主函数
    """
    start_time = time.time()
    
    # 创建输出目录
    os.makedirs(OUTPUT_PATH, exist_ok=True)
    
    print("\n" + "=" * 50)
    print(f"开始分析MultiVI模型结果: {PREFIX}")
    print("=" * 50)
    
    # 加载模型和数据
    model, adata = load_model_and_data(MODEL_PATH, ADATA_PATH)
    
    # 提取潜在表示
    latent = extract_latent_representation(model, OUTPUT_PATH, PREFIX)
    adata.obsm["X_multivi"] = latent
    
    # 检查是否有细胞类型信息
    has_cell_type = CELL_TYPE_KEY in adata.obs
    
    if has_cell_type:
        # 寻找最佳分辨率并进行聚类
        best_resolution, metrics_df = find_optimal_resolution(
            adata, "X_multivi", CELL_TYPE_KEY, OUTPUT_PATH, PREFIX
        )
        
        # 绘制指标曲线
        plot_metrics_vs_resolution(metrics_df, best_resolution, OUTPUT_PATH, PREFIX)
    else:
        print(f"\n未找到细胞类型信息 ({CELL_TYPE_KEY})，使用默认分辨率0.8进行聚类")
        sc.pp.neighbors(adata, use_rep="X_multivi", n_neighbors=30, key_added="multivi_neighbors")
        sc.tl.leiden(adata, neighbors_key="multivi_neighbors", key_added="leiden", resolution=0.8)
    
    # 生成可视化结果
    if has_cell_type:
        generate_visualizations(adata, CELL_TYPE_KEY, OUTPUT_PATH, PREFIX)
    else:
        # 计算UMAP
        if "X_umap" not in adata.obsm:
            sc.tl.umap(adata)
        
        # 可视化聚类结果
        clusters_plot_path = os.path.join(OUTPUT_PATH, f"{PREFIX}_umap_clusters.png")
        fig = sc.pl.umap(adata, color=["leiden"], return_fig=True)
        fig.savefig(clusters_plot_path, dpi=300, bbox_inches="tight")
        print(f"聚类UMAP可视化已保存到: {clusters_plot_path}")
    
    # 保存带有聚类结果的AnnData对象
    adata_save_path = os.path.join(OUTPUT_PATH, f"{PREFIX}_multivi.h5ad")
    adata.write(adata_save_path)
    print(f"带有聚类结果的AnnData对象已保存到: {adata_save_path}")
    
    # 计算运行时间
    end_time = time.time()
    run_time = end_time - start_time
    print(f"\n分析完成，耗时: {run_time:.2f} 秒")
    print("=" * 50)


if __name__ == "__main__":
    analyze_multivi_results()
