flowchart TD
    A[数据集准备] --> B[数据预处理]
    B --> C[模型训练]
    C --> D[性能评估]
    D --> E[算法对比]
    
    subgraph "数据集"
        A1[10x_lymph_node<br/>4,026细胞]
        A2[snare_p0<br/>5,081细胞]
        A3[paired_cellline<br/>2,789细胞]
        A4[neurips<br/>69,249细胞]
    end
    
    subgraph "预处理步骤"
        B1[质量控制<br/>min_genes=10<br/>min_cells=20]
        B2[特征选择<br/>高变基因]
        B3[细胞对齐<br/>模态匹配]
        B4[数据标准化]
    end
    
    subgraph "模型配置"
        C1[编码器设计<br/>模态特定网络]
        C2[潜在空间维度<br/>10-50维]
        C3[训练参数<br/>lr=2e-4<br/>早停机制]
        C4[GPU加速<br/>RTX 3060]
    end
    
    subgraph "评估指标"
        D1[聚类准确性<br/>ARI, NMI, V-measure]
        D2[批次效应校正<br/>iLISI, kBET]
        D3[可视化分析<br/>UMAP降维]
        D4[计算效率<br/>训练时间]
    end
    
    subgraph "对比算法"
        E1[MOFA+<br/>因子分析]
        E2[scAI<br/>张量分解]
        E3[scMVP<br/>变分自编码器]
        E4[MultiVI<br/>本研究方法]
    end
    
    A --> A1
    A --> A2
    A --> A3
    A --> A4
    
    B --> B1
    B --> B2
    B --> B3
    B --> B4
    
    C --> C1
    C --> C2
    C --> C3
    C --> C4
    
    D --> D1
    D --> D2
    D --> D3
    D --> D4
    
    E --> E1
    E --> E2
    E --> E3
    E --> E4
    
    style A4 fill:#ffeb3b
    style D2 fill:#ffcdd2
    style E4 fill:#c8e6c9
