#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
批次效应分析脚本 - CUDA加速版本
读取datasets/neuips/数据集下的两个h5ad文件，
选取s1d1,s2d2,s3d3批次进行批次效应iLISI,kBET测量并将结果可视化
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import scanpy as sc
import anndata as ad
import torch
from sklearn.neighbors import NearestNeighbors
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 设置随机种子
np.random.seed(42)
torch.manual_seed(42)

# 设置scanpy参数
sc.settings.verbosity = 2
sc.settings.set_figure_params(dpi=80, facecolor='white')

# 检查CUDA可用性
if torch.cuda.is_available():
    print(f"CUDA可用! GPU设备: {torch.cuda.get_device_name(0)}")
    print(f"CUDA版本: {torch.version.cuda}")
    device = torch.device('cuda')
else:
    print("CUDA不可用，使用CPU")
    device = torch.device('cpu')

def load_and_filter_data_optimized(rna_path, atac_path, target_batches=['s1d1', 's2d2', 's3d3']):
    """
    优化的数据加载和过滤函数
    """
    print("=== 加载数据 ===")

    # 加载RNA数据
    print(f"加载RNA数据: {rna_path}")
    adata_rna = sc.read_h5ad(rna_path)
    print(f"RNA数据形状: {adata_rna.shape}")

    # 加载ATAC数据
    print(f"加载ATAC数据: {atac_path}")
    adata_atac = sc.read_h5ad(atac_path)
    print(f"ATAC数据形状: {adata_atac.shape}")

    # 检查目标批次是否存在
    available_batches = set(adata_rna.obs['batch'].unique())
    target_set = set(target_batches)
    missing_batches = target_set - available_batches

    if missing_batches:
        print(f"警告: 以下批次不存在: {missing_batches}")
        target_batches = list(target_set & available_batches)
        print(f"将使用可用的批次: {target_batches}")

    if not target_batches:
        raise ValueError("没有找到任何目标批次!")

    # 过滤指定批次
    print(f"\n=== 过滤批次: {target_batches} ===")

    # 过滤RNA数据
    rna_mask = adata_rna.obs['batch'].isin(target_batches)
    adata_rna_filtered = adata_rna[rna_mask].copy()
    print(f"过滤后RNA数据形状: {adata_rna_filtered.shape}")

    # 过滤ATAC数据
    atac_mask = adata_atac.obs['batch'].isin(target_batches)
    adata_atac_filtered = adata_atac[atac_mask].copy()
    print(f"过滤后ATAC数据形状: {adata_atac_filtered.shape}")

    # 检查过滤后的批次分布
    print("\n过滤后的批次分布:")
    print("RNA:")
    print(adata_rna_filtered.obs['batch'].value_counts())
    print("ATAC:")
    print(adata_atac_filtered.obs['batch'].value_counts())

    return adata_rna_filtered, adata_atac_filtered

def calculate_ilisi_cuda(adata, batch_key='batch', k=90):
    """
    使用CUDA加速的iLISI计算
    """
    print(f"\n=== 计算iLISI (k={k}) - CUDA加速 ===")

    # 确保有neighbors图
    if 'neighbors' not in adata.uns:
        print("计算邻居图...")
        # 优先使用PCA表示，如果没有则使用原始数据
        if 'X_pca' in adata.obsm:
            print("使用PCA表示计算邻居图...")
            sc.pp.neighbors(adata, n_neighbors=k, use_rep='X_pca')
        else:
            print("使用原始数据计算邻居图...")
            sc.pp.neighbors(adata, n_neighbors=k, use_rep='X')

    # 获取批次信息
    batches = adata.obs[batch_key].values
    unique_batches = np.unique(batches)
    n_batches = len(unique_batches)

    print(f"批次数量: {n_batches}")
    print(f"批次: {unique_batches}")

    # 将批次转换为数值编码
    batch_to_idx = {batch: idx for idx, batch in enumerate(unique_batches)}
    batch_indices = np.array([batch_to_idx[batch] for batch in batches])

    # 获取邻居图并转换为tensor
    neighbors_matrix = adata.obsp['distances']

    # 使用更高效的方法计算iLISI
    ilisi_scores = []

    # 分批处理以节省内存，但确保处理所有细胞
    batch_size = 2000  # 增加批次大小以提高效率
    n_cells = adata.n_obs

    print(f"开始处理所有 {n_cells} 个细胞...")

    for start_idx in range(0, n_cells, batch_size):
        end_idx = min(start_idx + batch_size, n_cells)
        batch_scores = []

        for i in range(start_idx, end_idx):
            # 获取第i个细胞的k个最近邻
            neighbor_indices = neighbors_matrix[i].nonzero()[1][:k]
            neighbor_batch_indices = batch_indices[neighbor_indices]

            # 计算每个批次的比例
            batch_counts = np.bincount(neighbor_batch_indices, minlength=n_batches)
            batch_proportions = batch_counts / k

            # 计算Simpson指数的倒数
            simpson_index = np.sum(batch_proportions ** 2)
            ilisi_score = 1 / simpson_index if simpson_index > 0 else 1

            batch_scores.append(ilisi_score)

        ilisi_scores.extend(batch_scores)

        # 更频繁的进度报告
        if (start_idx // batch_size + 1) % 5 == 0 or end_idx == n_cells:
            progress = end_idx / n_cells * 100
            print(f"iLISI计算进度: {end_idx}/{n_cells} 个细胞 ({progress:.1f}%)")

    ilisi_scores = np.array(ilisi_scores)
    mean_ilisi = np.mean(ilisi_scores)

    print(f"平均iLISI: {mean_ilisi:.4f}")
    print(f"iLISI标准差: {np.std(ilisi_scores):.4f}")
    print(f"iLISI范围: [{np.min(ilisi_scores):.4f}, {np.max(ilisi_scores):.4f}]")

    return ilisi_scores, mean_ilisi

def calculate_kbet_cuda(adata, batch_key='batch', k=25, alpha=0.05):
    """
    使用CUDA加速的kBET计算
    """
    print(f"\n=== 计算kBET (k={k}, alpha={alpha}) - CUDA加速 ===")

    # 确保有neighbors图
    if 'neighbors' not in adata.uns:
        print("计算邻居图...")
        # 优先使用PCA表示，如果没有则使用原始数据
        if 'X_pca' in adata.obsm:
            print("使用PCA表示计算邻居图...")
            sc.pp.neighbors(adata, n_neighbors=k, use_rep='X_pca')
        else:
            print("使用原始数据计算邻居图...")
            sc.pp.neighbors(adata, n_neighbors=k, use_rep='X')

    # 获取批次信息
    batches = adata.obs[batch_key].values
    unique_batches = np.unique(batches)
    n_batches = len(unique_batches)

    # 计算全局批次分布
    global_batch_freq = pd.Series(batches).value_counts(normalize=True)

    print(f"全局批次分布:")
    for batch in unique_batches:
        print(f"  {batch}: {global_batch_freq[batch]:.4f}")

    # 将批次转换为数值编码
    batch_to_idx = {batch: idx for idx, batch in enumerate(unique_batches)}
    batch_indices = np.array([batch_to_idx[batch] for batch in batches])

    # 获取邻居图
    neighbors_matrix = adata.obsp['distances']

    kbet_pvalues = []

    # 分批处理，确保处理所有细胞
    batch_size = 2000  # 增加批次大小以提高效率
    n_cells = adata.n_obs

    print(f"开始处理所有 {n_cells} 个细胞...")

    for start_idx in range(0, n_cells, batch_size):
        end_idx = min(start_idx + batch_size, n_cells)
        batch_pvalues = []

        for i in range(start_idx, end_idx):
            # 获取第i个细胞的k个最近邻
            neighbor_indices = neighbors_matrix[i].nonzero()[1][:k]
            neighbor_batch_indices = batch_indices[neighbor_indices]

            # 计算观察到的批次频率
            observed_counts = np.bincount(neighbor_batch_indices, minlength=n_batches)

            # 计算期望频率
            expected_counts = np.array([global_batch_freq[batch] * k for batch in unique_batches])

            # 进行卡方检验
            chi2_stat = np.sum((observed_counts - expected_counts) ** 2 /
                              np.maximum(expected_counts, 1e-10))

            # 计算p值
            df = n_batches - 1
            p_value = 1 - stats.chi2.cdf(chi2_stat, df)
            batch_pvalues.append(p_value)

        kbet_pvalues.extend(batch_pvalues)

        # 更频繁的进度报告
        if (start_idx // batch_size + 1) % 5 == 0 or end_idx == n_cells:
            progress = end_idx / n_cells * 100
            print(f"kBET计算进度: {end_idx}/{n_cells} 个细胞 ({progress:.1f}%)")

    kbet_pvalues = np.array(kbet_pvalues)
    kbet_accept_rate = np.mean(kbet_pvalues > alpha)

    print(f"kBET接受率: {kbet_accept_rate:.4f}")
    print(f"平均p值: {np.mean(kbet_pvalues):.4f}")
    print(f"p值中位数: {np.median(kbet_pvalues):.4f}")

    return kbet_pvalues, kbet_accept_rate

def create_combined_data_optimized(adata_rna, adata_atac, max_features=10000):
    """
    创建合并多模态数据，使用完整数据集
    """
    print("\n=== 创建合并数据 (完整数据版本) ===")

    # 确保细胞顺序一致
    common_cells = adata_rna.obs.index.intersection(adata_atac.obs.index)
    print(f"共同细胞数量: {len(common_cells)}")

    # 使用所有细胞，不进行采样
    print("使用完整数据集进行分析...")

    # 重新排序
    adata_rna_aligned = adata_rna[common_cells].copy()
    adata_atac_aligned = adata_atac[common_cells].copy()

    # 选择高变基因/峰以减少计算量
    print("选择高变特征以优化性能...")

    # 数据预处理和清理
    print("清理数据中的无穷大值和NaN值...")

    # 处理RNA数据
    # 替换无穷大值和NaN值
    if hasattr(adata_rna_aligned.X, 'data'):
        # 稀疏矩阵
        adata_rna_aligned.X.data = np.nan_to_num(adata_rna_aligned.X.data,
                                                 nan=0.0, posinf=0.0, neginf=0.0)
    else:
        # 密集矩阵
        adata_rna_aligned.X = np.nan_to_num(adata_rna_aligned.X,
                                           nan=0.0, posinf=0.0, neginf=0.0)

    # 处理ATAC数据
    if hasattr(adata_atac_aligned.X, 'data'):
        # 稀疏矩阵
        adata_atac_aligned.X.data = np.nan_to_num(adata_atac_aligned.X.data,
                                                  nan=0.0, posinf=0.0, neginf=0.0)
    else:
        # 密集矩阵
        adata_atac_aligned.X = np.nan_to_num(adata_atac_aligned.X,
                                            nan=0.0, posinf=0.0, neginf=0.0)

    # 基本过滤
    sc.pp.filter_genes(adata_rna_aligned, min_cells=10)
    sc.pp.filter_genes(adata_atac_aligned, min_cells=10)

    # 选择更多特征进行完整分析
    max_rna_features = min(5000, adata_rna_aligned.n_vars)  # 增加到5000个基因
    max_atac_features = min(8000, adata_atac_aligned.n_vars)  # 增加到8000个峰

    # RNA高变基因
    try:
        sc.pp.highly_variable_genes(adata_rna_aligned, n_top_genes=max_rna_features)
        adata_rna_hv = adata_rna_aligned[:, adata_rna_aligned.var.highly_variable].copy()
    except Exception as e:
        print(f"RNA高变基因选择失败，使用前{max_rna_features}个基因: {e}")
        adata_rna_hv = adata_rna_aligned[:, :max_rna_features].copy()

    # ATAC高变峰
    try:
        sc.pp.highly_variable_genes(adata_atac_aligned, n_top_genes=max_atac_features)
        adata_atac_hv = adata_atac_aligned[:, adata_atac_aligned.var.highly_variable].copy()
    except Exception as e:
        print(f"ATAC高变峰选择失败，使用前{max_atac_features}个峰: {e}")
        adata_atac_hv = adata_atac_aligned[:, :max_atac_features].copy()

    print(f"选择的RNA特征数: {adata_rna_hv.n_vars}")
    print(f"选择的ATAC特征数: {adata_atac_hv.n_vars}")

    # 合并特征矩阵
    import scipy.sparse as sp

    if sp.issparse(adata_rna_hv.X):
        X_rna = adata_rna_hv.X
    else:
        X_rna = sp.csr_matrix(adata_rna_hv.X)

    if sp.issparse(adata_atac_hv.X):
        X_atac = adata_atac_hv.X
    else:
        X_atac = sp.csr_matrix(adata_atac_hv.X)

    # 水平拼接特征矩阵
    X_combined = sp.hstack([X_rna, X_atac])

    # 创建合并的var信息
    var_rna = adata_rna_hv.var.copy()
    var_rna['modality'] = 'RNA'
    var_atac = adata_atac_hv.var.copy()
    var_atac['modality'] = 'ATAC'

    var_combined = pd.concat([var_rna, var_atac])

    # 创建合并的AnnData对象
    adata_combined = ad.AnnData(
        X=X_combined,
        obs=adata_rna_aligned.obs.copy(),
        var=var_combined
    )

    print(f"合并数据形状: {adata_combined.shape}")

    return adata_combined

def run_batch_analysis(target_batches, analysis_name=""):
    """
    运行批次效应分析

    参数:
        target_batches: 目标批次列表
        analysis_name: 分析名称，用于区分不同的分析结果
    """
    print(f"批次效应分析脚本 - CUDA加速版本 {analysis_name}")
    print("=" * 60)

    # 设置路径
    data_dir = "../datasets/neuips"
    rna_path = os.path.join(data_dir, "RNA_unpred.h5ad")
    atac_path = os.path.join(data_dir, "ATAC_unpred.h5ad")

    # 创建输出目录结构: output/batch_effect_analysis/批次组合/
    base_output_dir = "output"
    analysis_dir = os.path.join(base_output_dir, "batch_effect_analysis")
    batch_suffix = "_".join(target_batches)
    output_dir = os.path.join(analysis_dir, f"batches_{batch_suffix}")

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    print(f"目标批次: {target_batches}")
    print(f"输出目录: {output_dir}")

    try:
        # 加载和过滤数据
        adata_rna, adata_atac = load_and_filter_data_optimized(rna_path, atac_path, target_batches)

        # 创建合并数据
        adata_combined = create_combined_data_optimized(adata_rna, adata_atac)

        # 数据预处理
        print("\n=== 数据预处理 ===")
        sc.pp.normalize_total(adata_combined, target_sum=1e4)
        sc.pp.log1p(adata_combined)

        # 清理var中的问题列
        print("清理变量注释...")
        # 确保highly_variable列是布尔类型
        if 'highly_variable' in adata_combined.var.columns:
            adata_combined.var['highly_variable'] = adata_combined.var['highly_variable'].astype(bool)

        # 计算PCA
        print("计算PCA...")
        try:
            sc.tl.pca(adata_combined, svd_solver='arpack', n_comps=30)  # 减少PCA组件数
        except Exception as e:
            print(f"PCA计算失败，尝试其他方法: {e}")
            # 移除可能有问题的列
            if 'highly_variable' in adata_combined.var.columns:
                del adata_combined.var['highly_variable']
            sc.tl.pca(adata_combined, svd_solver='arpack', n_comps=30)

        # 使用PCA结果进行后续计算以加速
        print("使用PCA表示进行后续计算...")

        # 计算批次效应指标 (使用更小的k值以加速)
        ilisi_scores, mean_ilisi = calculate_ilisi_cuda(adata_combined, batch_key='batch', k=30)  # 减少k值
        kbet_pvalues, kbet_accept_rate = calculate_kbet_cuda(adata_combined, batch_key='batch', k=15)  # 减少k值

        # 保存指标到obs
        adata_combined.obs['ilisi'] = ilisi_scores
        adata_combined.obs['kbet_pvalue'] = kbet_pvalues

        print(f"\n=== 批次效应分析结果 ===")
        print(f"平均iLISI: {mean_ilisi:.4f}")
        print(f"kBET接受率: {kbet_accept_rate:.4f}")

        # 保存结果
        results_summary = {
            'metric': ['iLISI', 'kBET'],
            'value': [mean_ilisi, kbet_accept_rate],
            'description': ['平均iLISI分数', 'kBET接受率']
        }

        results_df = pd.DataFrame(results_summary)
        results_path = os.path.join(output_dir, 'batch_effect_metrics.csv')
        results_df.to_csv(results_path, index=False)
        print(f"\n结果已保存到: {results_path}")

        # 保存处理后的数据
        adata_path = os.path.join(output_dir, 'processed_data.h5ad')
        adata_combined.write(adata_path)
        print(f"处理后的数据已保存到: {adata_path}")

        # 创建可视化
        create_visualizations_optimized(adata_combined, output_dir)

        print("\n=== 分析完成 ===")

    except Exception as e:
        print(f"错误: {str(e)}")
        import traceback
        traceback.print_exc()

def create_visualizations_optimized(adata, output_dir):
    """
    创建优化的批次效应分析可视化图表，包含结果数据
    """
    print("\n=== 创建可视化 ===")

    # 计算UMAP
    print("计算UMAP...")
    sc.tl.umap(adata, min_dist=0.3, spread=1.0)

    # 设置字体以避免中文显示问题
    import matplotlib
    matplotlib.rcParams['font.family'] = ['DejaVu Sans', 'Arial', 'sans-serif']
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['figure.figsize'] = (12, 8)
    plt.rcParams['font.size'] = 12

    # 计算关键统计数据
    mean_ilisi = np.mean(adata.obs['ilisi'])
    std_ilisi = np.std(adata.obs['ilisi'])
    kbet_accept_rate = np.mean(adata.obs['kbet_pvalue'] > 0.05)
    mean_kbet_pvalue = np.mean(adata.obs['kbet_pvalue'])
    n_cells = adata.n_obs
    n_features = adata.n_vars
    batches = ', '.join(adata.obs['batch'].unique())

    # 1. 创建主要的UMAP图
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))

    # 按批次着色的UMAP
    sc.pl.umap(adata, color='batch', ax=axes[0,0], show=False, frameon=False,
               title='UMAP - Colored by Batch')

    # 在批次图上添加数据信息
    axes[0,0].text(0.02, 0.98, f'Batches: {batches}\nCells: {n_cells:,}\nFeatures: {n_features:,}',
                   transform=axes[0,0].transAxes, fontsize=10, verticalalignment='top',
                   bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

    # 按细胞类型着色的UMAP
    sc.pl.umap(adata, color='cell_type', ax=axes[0,1], show=False, frameon=False,
               title='UMAP - Colored by Cell Type')

    # 在细胞类型图上添加细胞类型数量信息
    n_celltypes = len(adata.obs['cell_type'].unique())
    axes[0,1].text(0.02, 0.98, f'Cell Types: {n_celltypes}\nTotal Cells: {n_cells:,}',
                   transform=axes[0,1].transAxes, fontsize=10, verticalalignment='top',
                   bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

    # iLISI分数分布
    sc.pl.umap(adata, color='ilisi', ax=axes[1,0], show=False, frameon=False,
               cmap='viridis', title='UMAP - iLISI Score')

    # 在iLISI图上添加统计信息
    axes[1,0].text(0.02, 0.98, f'Mean iLISI: {mean_ilisi:.3f} ± {std_ilisi:.3f}\nRange: [{np.min(adata.obs["ilisi"]):.3f}, {np.max(adata.obs["ilisi"]):.3f}]',
                   transform=axes[1,0].transAxes, fontsize=10, verticalalignment='top',
                   bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

    # kBET p值分布
    sc.pl.umap(adata, color='kbet_pvalue', ax=axes[1,1], show=False, frameon=False,
               cmap='plasma', title='UMAP - kBET p-value')

    # 在kBET图上添加统计信息
    axes[1,1].text(0.02, 0.98, f'kBET Accept Rate: {kbet_accept_rate:.3f}\nMean p-value: {mean_kbet_pvalue:.3f}',
                   transform=axes[1,1].transAxes, fontsize=10, verticalalignment='top',
                   bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

    plt.tight_layout()
    umap_path = os.path.join(output_dir, 'umap_analysis.png')
    plt.savefig(umap_path, dpi=300, bbox_inches='tight')
    plt.close()
    print(f"UMAP图已保存到: {umap_path}")

    # 2. 批次效应指标分布图
    fig, axes = plt.subplots(2, 2, figsize=(16, 10))

    # iLISI分数分布直方图
    axes[0,0].hist(adata.obs['ilisi'], bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    axes[0,0].set_xlabel('iLISI Score')
    axes[0,0].set_ylabel('Frequency')
    axes[0,0].set_title('iLISI Score Distribution')
    axes[0,0].axvline(mean_ilisi, color='red', linestyle='--',
                     label=f'Mean: {mean_ilisi:.3f}')
    axes[0,0].legend()

    # 添加详细统计信息
    axes[0,0].text(0.98, 0.98, f'Mean: {mean_ilisi:.3f}\nStd: {std_ilisi:.3f}\nMedian: {np.median(adata.obs["ilisi"]):.3f}\nCells: {n_cells:,}',
                   transform=axes[0,0].transAxes, fontsize=9, verticalalignment='top', horizontalalignment='right',
                   bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

    # kBET p值分布直方图
    axes[0,1].hist(adata.obs['kbet_pvalue'], bins=50, alpha=0.7, color='lightcoral', edgecolor='black')
    axes[0,1].set_xlabel('kBET p-value')
    axes[0,1].set_ylabel('Frequency')
    axes[0,1].set_title('kBET p-value Distribution')
    axes[0,1].axvline(mean_kbet_pvalue, color='blue', linestyle='--',
                     label=f'Mean: {mean_kbet_pvalue:.3f}')
    axes[0,1].legend()

    # 添加详细统计信息
    median_kbet = np.median(adata.obs['kbet_pvalue'])
    axes[0,1].text(0.98, 0.98, f'Accept Rate: {kbet_accept_rate:.3f}\nMean p-val: {mean_kbet_pvalue:.3f}\nMedian p-val: {median_kbet:.3f}\nCells: {n_cells:,}',
                   transform=axes[0,1].transAxes, fontsize=9, verticalalignment='top', horizontalalignment='right',
                   bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

    # 按批次的iLISI分数箱线图
    batch_ilisi_data = [adata.obs[adata.obs['batch'] == batch]['ilisi'].values
                        for batch in adata.obs['batch'].unique()]
    axes[1,0].boxplot(batch_ilisi_data, labels=adata.obs['batch'].unique())
    axes[1,0].set_xlabel('Batch')
    axes[1,0].set_ylabel('iLISI Score')
    axes[1,0].set_title('iLISI Score by Batch')
    axes[1,0].tick_params(axis='x', rotation=45)

    # 按批次的kBET接受率
    batch_kbet_accept = []
    batch_names = []
    for batch in adata.obs['batch'].unique():
        batch_mask = adata.obs['batch'] == batch
        accept_rate = np.mean(adata.obs[batch_mask]['kbet_pvalue'] > 0.05)
        batch_kbet_accept.append(accept_rate)
        batch_names.append(batch)

    axes[1,1].bar(batch_names, batch_kbet_accept, alpha=0.7, color='lightgreen', edgecolor='black')
    axes[1,1].set_xlabel('Batch')
    axes[1,1].set_ylabel('kBET Accept Rate')
    axes[1,1].set_title('kBET Accept Rate by Batch')
    axes[1,1].tick_params(axis='x', rotation=45)

    # 添加水平线表示总体接受率
    overall_accept_rate = np.mean(adata.obs['kbet_pvalue'] > 0.05)
    axes[1,1].axhline(overall_accept_rate, color='red', linestyle='--',
                     label=f'Overall Rate: {overall_accept_rate:.3f}')
    axes[1,1].legend()

    plt.tight_layout()
    metrics_path = os.path.join(output_dir, 'batch_effect_metrics.png')
    plt.savefig(metrics_path, dpi=300, bbox_inches='tight')
    plt.close()
    print(f"批次效应指标图已保存到: {metrics_path}")

    # 3. 创建批次分布图
    print("创建批次分布图...")
    fig, axes = plt.subplots(1, 2, figsize=(14, 6))

    # 批次细胞数量分布
    batch_counts = adata.obs['batch'].value_counts()
    axes[0].bar(batch_counts.index, batch_counts.values, alpha=0.7, color='orange', edgecolor='black')
    axes[0].set_xlabel('Batch')
    axes[0].set_ylabel('Number of Cells')
    axes[0].set_title('Cell Count by Batch')
    axes[0].tick_params(axis='x', rotation=45)

    # 添加数值标签
    for i, v in enumerate(batch_counts.values):
        axes[0].text(i, v + max(batch_counts.values) * 0.01, str(v),
                    ha='center', va='bottom', fontweight='bold')

    # 细胞类型在各批次中的分布
    batch_celltype_counts = pd.crosstab(adata.obs['batch'], adata.obs['cell_type'])
    batch_celltype_counts.plot(kind='bar', stacked=True, ax=axes[1], alpha=0.8)
    axes[1].set_xlabel('Batch')
    axes[1].set_ylabel('Number of Cells')
    axes[1].set_title('Cell Type Distribution by Batch')
    axes[1].tick_params(axis='x', rotation=45)
    axes[1].legend(bbox_to_anchor=(1.05, 1), loc='upper left')

    plt.tight_layout()
    distribution_path = os.path.join(output_dir, 'batch_distribution.png')
    plt.savefig(distribution_path, dpi=300, bbox_inches='tight')
    plt.close()
    print(f"批次分布图已保存到: {distribution_path}")

    # 4. 创建综合报告
    create_summary_report_optimized(adata, output_dir)

def create_summary_report_optimized(adata, output_dir):
    """
    创建优化的批次效应分析综合报告
    """
    print("\n=== 创建综合报告 ===")

    # 计算统计指标
    mean_ilisi = np.mean(adata.obs['ilisi'])
    std_ilisi = np.std(adata.obs['ilisi'])
    median_ilisi = np.median(adata.obs['ilisi'])

    kbet_accept_rate = np.mean(adata.obs['kbet_pvalue'] > 0.05)
    mean_kbet_pvalue = np.mean(adata.obs['kbet_pvalue'])
    median_kbet_pvalue = np.median(adata.obs['kbet_pvalue'])

    # 按批次统计
    batch_stats = []
    for batch in adata.obs['batch'].unique():
        batch_mask = adata.obs['batch'] == batch
        batch_data = adata.obs[batch_mask]

        batch_stat = {
            'batch': batch,
            'n_cells': np.sum(batch_mask),
            'mean_ilisi': np.mean(batch_data['ilisi']),
            'std_ilisi': np.std(batch_data['ilisi']),
            'kbet_accept_rate': np.mean(batch_data['kbet_pvalue'] > 0.05),
            'mean_kbet_pvalue': np.mean(batch_data['kbet_pvalue'])
        }
        batch_stats.append(batch_stat)

    batch_stats_df = pd.DataFrame(batch_stats)

    # 创建报告文本
    report = f"""
批次效应分析报告 (CUDA加速版本)
================================

数据概览:
--------
- 总细胞数: {adata.n_obs:,}
- 总特征数: {adata.n_vars:,}
- 分析批次: {', '.join(adata.obs['batch'].unique())}
- 细胞类型数: {len(adata.obs['cell_type'].unique())}

批次效应指标:
-----------
iLISI (integrated Local Inverse Simpson Index):
- 平均值: {mean_ilisi:.4f}
- 标准差: {std_ilisi:.4f}
- 中位数: {median_ilisi:.4f}
- 解释: iLISI值越高表示批次混合越好，理想值接近批次数量({len(adata.obs['batch'].unique())})

kBET (k-nearest neighbor Batch Effect Test):
- 接受率: {kbet_accept_rate:.4f}
- 平均p值: {mean_kbet_pvalue:.4f}
- 中位数p值: {median_kbet_pvalue:.4f}
- 解释: 接受率越高表示批次效应越小，理想值接近1.0

各批次详细统计:
--------------
"""

    for _, row in batch_stats_df.iterrows():
        report += f"""
批次 {row['batch']}:
  - 细胞数: {row['n_cells']:,}
  - 平均iLISI: {row['mean_ilisi']:.4f} ± {row['std_ilisi']:.4f}
  - kBET接受率: {row['kbet_accept_rate']:.4f}
  - 平均kBET p值: {row['mean_kbet_pvalue']:.4f}
"""

    report += f"""
结论:
----
基于iLISI和kBET指标的分析结果:
- iLISI平均分数为{mean_ilisi:.4f}，{'表明批次混合良好' if mean_ilisi > 2.0 else '表明仍存在一定批次效应'}
- kBET接受率为{kbet_accept_rate:.4f}，{'表明批次效应较小' if kbet_accept_rate > 0.7 else '表明存在明显批次效应'}

建议:
----
{'当前数据的批次效应已得到较好控制。' if mean_ilisi > 2.0 and kbet_accept_rate > 0.7 else '建议进一步进行批次效应校正处理。'}

技术说明:
--------
- 本分析使用CUDA加速计算，提高了处理效率
- 为优化性能，选择了高变特征进行分析
- 使用分批处理策略以节省内存
"""

    # 保存报告
    report_path = os.path.join(output_dir, 'batch_effect_report.txt')
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report)

    # 保存批次统计表
    batch_stats_path = os.path.join(output_dir, 'batch_statistics.csv')
    batch_stats_df.to_csv(batch_stats_path, index=False)

    print(f"综合报告已保存到: {report_path}")
    print(f"批次统计表已保存到: {batch_stats_path}")

def main():
    """主函数 - 运行s1d1, s1d2, s1d3批次分析"""
    print("批次效应分析脚本 - s1d1, s1d2, s1d3批次分析")
    print("=" * 60)

    # 分析: s1d1, s1d2, s1d3
    print("\n🔬 批次分析: s1d1, s1d2, s1d3")
    target_batches = ['s1d1', 's1d2', 's1d3']
    run_batch_analysis(target_batches, "s1系列分析")

    print("\n" + "="*60)
    print("🎉 分析完成!")
    print("="*60)
    print("结果文件夹:")
    print(f"  - s1d1, s1d2, s1d3: output/batch_effect_analysis/batches_s1d1_s1d2_s1d3/")
    print(f"\n📁 主输出目录: output/batch_effect_analysis/")
    print("分析结果已保存在独立的子文件夹中")

if __name__ == "__main__":
    main()
