
批次效应分析报告 (CUDA加速版本)
================================

数据概览:
--------
- 总细胞数: 17,243
- 总特征数: 13,306
- 分析批次: s1d1, s1d2, s1d3
- 细胞类型数: 22

批次效应指标:
-----------
iLISI (integrated Local Inverse Simpson Index):
- 平均值: 1.9168
- 标准差: 0.5114
- 中位数: 1.9231
- 解释: iLISI值越高表示批次混合越好，理想值接近批次数量(3)

kBET (k-nearest neighbor Batch Effect Test):
- 接受率: 0.3236
- 平均p值: 0.1025
- 中位数p值: 0.0105
- 解释: 接受率越高表示批次效应越小，理想值接近1.0

各批次详细统计:
--------------

批次 s1d1:
  - 细胞数: 6,224
  - 平均iLISI: 1.9304 ± 0.4777
  - kBET接受率: 0.3397
  - 平均kBET p值: 0.1088

批次 s1d2:
  - 细胞数: 6,740
  - 平均iLISI: 1.8230 ± 0.5191
  - kBET接受率: 0.3380
  - 平均kBET p值: 0.1006

批次 s1d3:
  - 细胞数: 4,279
  - 平均iLISI: 2.0445 ± 0.5166
  - kBET接受率: 0.2776
  - 平均kBET p值: 0.0964

结论:
----
基于iLISI和kBET指标的分析结果:
- iLISI平均分数为1.9168，表明仍存在一定批次效应
- kBET接受率为0.3236，表明存在明显批次效应

建议:
----
建议进一步进行批次效应校正处理。

技术说明:
--------
- 本分析使用CUDA加速计算，提高了处理效率
- 为优化性能，选择了高变特征进行分析
- 使用分批处理策略以节省内存
