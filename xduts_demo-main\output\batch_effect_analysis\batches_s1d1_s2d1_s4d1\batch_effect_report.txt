
批次效应分析报告 (CUDA加速版本)
================================

数据概览:
--------
- 总细胞数: 18,467
- 总特征数: 13,012
- 分析批次: s1d1, s2d1, s4d1
- 细胞类型数: 21

批次效应指标:
-----------
iLISI (integrated Local Inverse Simpson Index):
- 平均值: 1.2760
- 标准差: 0.4025
- 中位数: 1.0689
- 解释: iLISI值越高表示批次混合越好，理想值接近批次数量(3)

kBET (k-nearest neighbor Batch Effect Test):
- 接受率: 0.0254
- 平均p值: 0.0071
- 中位数p值: 0.0001
- 解释: 接受率越高表示批次效应越小，理想值接近1.0

各批次详细统计:
--------------

批次 s1d1:
  - 细胞数: 6,224
  - 平均iLISI: 1.3503 ± 0.3945
  - kBET接受率: 0.0233
  - 平均kBET p值: 0.0076

批次 s2d1:
  - 细胞数: 4,220
  - 平均iLISI: 1.5376 ± 0.4610
  - kBET接受率: 0.0360
  - 平均kBET p值: 0.0090

批次 s4d1:
  - 细胞数: 8,023
  - 平均iLISI: 1.0807 ± 0.2489
  - kBET接受率: 0.0214
  - 平均kBET p值: 0.0057

结论:
----
基于iLISI和kBET指标的分析结果:
- iLISI平均分数为1.2760，表明仍存在一定批次效应
- kBET接受率为0.0254，表明存在明显批次效应

建议:
----
建议进一步进行批次效应校正处理。

技术说明:
--------
- 本分析使用CUDA加速计算，提高了处理效率
- 为优化性能，选择了高变特征进行分析
- 使用分批处理策略以节省内存
