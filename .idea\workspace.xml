<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="5c418449-7e54-4509-a30e-327e38f7b253" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/xduts_demo" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="2tGGnF0FPBJuefs0BCuQxMwy4ts" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Python.MOFA-torch.executor": "Run",
    "Python.MOFA.executor": "Run",
    "Python.MultiVI-new.executor": "Run",
    "Python.MultiVI.executor": "Run",
    "Python.MutiVI.executor": "Run",
    "Python.Seurat.executor": "Run",
    "Python.adata.executor": "Run",
    "Python.check.executor": "Run",
    "Python.h5ad.executor": "Run",
    "Python.leiden.executor": "Run",
    "Python.multivi_analysis.executor": "Run",
    "Python.preSolve.executor": "Run",
    "Python.scAI.executor": "Run",
    "Python.scMVP.executor": "Run",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "git-widget-placeholder": "main",
    "last_opened_file_path": "D:/MultiVI/datasets/10x_lymph_node/code",
    "settings.editor.selected.configurable": "com.jetbrains.python.configuration.PyActiveSdkModuleConfigurable"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\MultiVI\datasets\10x_lymph_node\code" />
      <recent name="D:\MultiVI\Python" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\MultiVI\Python" />
      <recent name="D:\MultiVI" />
      <recent name="D:\MultiVI\datasets\10x_pbmc" />
      <recent name="D:\MultiVI\datasets\neuips" />
    </key>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-python-sdk-0509580d9d50-746f403e7f0c-com.jetbrains.pycharm.community.sharedIndexes.bundled-PC-241.14494.241" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="5c418449-7e54-4509-a30e-327e38f7b253" name="Changes" comment="" />
      <created>1739971162915</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1739971162915</updated>
    </task>
    <servers />
  </component>
</project>