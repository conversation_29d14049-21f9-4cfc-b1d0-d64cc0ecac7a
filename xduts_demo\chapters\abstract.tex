单细胞多组学测序技术能够同时测量同一细胞的基因表达、染色质可及性等多种分子特征，为深入研究细胞异质性与调控网络提供了强大工具。然而，这些数据具有高维、稀疏、模态间异质性大以及存在批次效应等挑战。特别是数据集中常混合包含配对（多模态）和非配对（单模态）细胞，需要专门方法进行有效集成。\textbf{MultiVI} 是一种基于深度生成模型（变分自编码器）的先进方法，能够整合配对与非配对单细胞数据，构建联合潜在表示空间，并具备推断缺失模态的能力。本研究旨在深入理解 MultiVI 原理并进行实现，同时与其他主流集成算法（如 scAI、MOFA+、scMVP、Seurat）进行比较评估。研究基于 Python 环境，实现了 MultiVI 核心算法，并在选定的单细胞多组学数据集上进行初步测试，取得了良好的集成效果。后续将完成对比算法实现，使用 ARI、NMI、cASW 等指标全面评估各算法在细胞类型聚类、批次校正和生物学信息保留上的性能。本研究将为单细胞多组学数据集成提供实现参考和对比分析，具有重要的科学和应用价值。

\textbf{关键词}：单细胞多组学；数据集成；MultiVI；深度学习
