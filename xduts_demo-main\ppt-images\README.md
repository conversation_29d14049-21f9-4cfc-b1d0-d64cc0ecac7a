# PPT架构图使用说明

本文件夹包含了为您的MultiVI单细胞多组学数据集成研究生成的6个系统架构图，这些图表可以直接用于PPT演示。

## 图表列表

### 1. 系统整体架构图 (01-system-architecture.png)
**用途**: 展示MultiVI系统的完整架构
**适用场景**: 
- 研究背景介绍
- 系统概述
- 技术路线说明

**内容要点**:
- 数据输入层：scRNA-seq、scATAC-seq、蛋白质数据
- 数据预处理模块：质量控制、标准化、特征选择、细胞对齐
- MultiVI核心模型：编码器、潜在空间整合、解码器
- 训练优化模块：损失函数、批次效应校正、对抗训练
- 输出分析层：细胞聚类、批次效应评估、可视化分析、数据推断

### 2. 变分自编码器模型架构图 (02-vae-architecture.png)
**用途**: 详细展示MultiVI的VAE模型结构
**适用场景**:
- 方法原理介绍
- 技术细节说明
- 模型架构讲解

**内容要点**:
- 输入数据：RNA表达矩阵、ATAC可及性矩阵、批次信息
- 编码器网络：模态特定的编码器
- 潜在空间：RNA和ATAC潜在表示及其整合
- 对齐损失：对称KL散度
- 解码器网络：重构各模态数据
- 损失函数：ELBO损失函数

### 3. 数据集成策略分类图 (03-integration-strategies.png)
**用途**: 展示单细胞多组学数据集成的分类方法
**适用场景**:
- 相关工作介绍
- 方法对比
- 研究定位

**内容要点**:
- 按数据结构分类：垂直集成、水平集成、马赛克集成
- 按核心技术分类：降维对齐、矩阵分解、图方法、深度学习
- MultiVI在马赛克集成和深度学习方法中的定位

### 4. 实验流程与评估体系 (04-experiment-workflow.png)
**用途**: 展示完整的实验设计和评估流程
**适用场景**:
- 实验设计介绍
- 方法论说明
- 评估体系展示

**内容要点**:
- 数据集：4个数据集的基本信息
- 预处理步骤：质量控制、特征选择、细胞对齐、数据标准化
- 模型配置：编码器设计、潜在空间维度、训练参数、GPU加速
- 评估指标：聚类准确性、批次效应校正、可视化分析、计算效率
- 对比算法：MOFA+、scAI、scMVP、MultiVI

### 5. 技术栈与依赖关系 (05-tech-stack.png)
**用途**: 展示实现所用的技术栈
**适用场景**:
- 实现细节介绍
- 技术选型说明
- 系统环境展示

**内容要点**:
- 硬件层：CPU、GPU、内存配置
- 系统层：操作系统、Python版本
- 深度学习框架：PyTorch、CUDA支持
- 单细胞分析工具：scvi-tools、Scanpy、AnnData
- 数据处理库：NumPy、Pandas、SciPy、scikit-learn
- 可视化工具：Matplotlib、Seaborn、UMAP-learn
- MultiVI核心组件：变分自编码器、编码器网络、解码器网络、损失函数、优化器

### 6. 数据处理流水线 (06-data-pipeline.png)
**用途**: 展示从原始数据到模型输入的完整处理流程
**适用场景**:
- 数据预处理介绍
- 技术实现细节
- 流程标准化展示

**内容要点**:
- 原始数据：MTX格式的scRNA-seq和scATAC-seq数据、CSV格式的细胞元数据
- 数据加载：load_modality函数、CSR稀疏矩阵、特征名称匹配、细胞条码对齐
- 质量控制：细胞过滤、基因过滤、峰值过滤、质量指标计算
- 数据标准化：文库大小标准化、对数变换、高变基因选择、Z-score标准化
- 数据整合：organize_multiome_anndatas、模态标识添加、AnnData对象合并、批次信息配置
- 模型输入：训练集、验证集、测试集、MULTIVI.setup_anndata

## 使用建议

### PPT中的使用方式
1. **插入图片**: 直接将PNG文件插入到PPT中
2. **调整大小**: 图片已经以1200x800像素生成，适合全屏显示
3. **添加标题**: 为每个图表添加合适的标题
4. **配合文字**: 结合图表内容添加关键点说明

### 演示建议
1. **循序渐进**: 按照图表编号顺序介绍，从整体到细节
2. **重点突出**: 利用图表中的颜色标记来强调重点内容
3. **互动讲解**: 可以指向图表的具体部分进行详细说明
4. **时间控制**: 每个图表建议讲解2-3分钟

### 自定义修改
如果需要修改图表：
1. 编辑对应的.mmd文件
2. 使用以下命令重新生成PNG：
   ```bash
   npx @mermaid-js/mermaid-cli -i [文件名].mmd -o [文件名].png -w 1200 -H 800 --backgroundColor white
   ```

## 图表特色

- **层次清晰**: 每个图都有明确的模块划分
- **信息丰富**: 包含了关键的技术细节和参数
- **视觉友好**: 使用了不同的颜色来突出重点
- **逻辑性强**: 展示了清晰的数据流和处理逻辑
- **专业性高**: 符合学术论文和技术演示的标准

这些图表将大大提升您PPT的专业性和清晰度，帮助听众更好地理解您的研究内容和技术实现。
