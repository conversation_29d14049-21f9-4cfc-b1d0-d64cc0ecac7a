import gzip
import os
from pathlib import Path

import numpy as np
import pooch
import scvi
import scanpy as sc
import seaborn as sns
import torch

# 设置随机种子和图形参数
import torch

def setup():
    scvi.settings.seed = 0
    print("Last run with scvi-tools version:", scvi.__version__)
    sc.set_figure_params(figsize=(6, 6), frameon=False)
    sns.set_theme()
    torch.set_float32_matmul_precision("high")

# 数据获取
def download_data(save_path: str, fname: str = "pbmc_10k"):
    url = "https://cf.10xgenomics.com/samples/cell-arc/2.0.0/pbmc_unsorted_10k/pbmc_unsorted_10k_filtered_feature_bc_matrix.tar.gz"
    known_hash = "872b0dba467d972aa498812a857677ca7cf69050d4f9762b2cd4753b2be694a1"

    data_paths = pooch.retrieve(
        url=url,
        known_hash=known_hash,
        fname=fname,
        path=save_path,
        processor=pooch.Untar(),
        progressbar=True,
    )

    data_paths.sort()

    for path in data_paths:
        with gzip.open(path, "rb") as f_in:
            with open(path.replace(".gz", ""), "wb") as f_out:
                f_out.write(f_in.read())

    return str(Path(data_paths[0]).parent)

def download_satpathy_data(save_path: str):
    url = "https://ftp.ncbi.nlm.nih.gov/geo/series/GSE129nnn/GSE129785/suppl/GSE129785_RAW.tar"

    data_paths = pooch.retrieve(
        url=url,
        known_hash=None,
        fname="GSE129785_RAW.tar",
        path=save_path,
        processor=pooch.Untar(),
        progressbar=True,
    )

    data_paths.sort()

    for path in data_paths:
        if path.endswith(".gz"):
            with gzip.open(path, "rb") as f_in:
                with open(path.replace(".gz", ""), "wb") as f_out:
                    f_out.write(f_in.read())

    return str(Path(data_paths[0]).parent)

# 数据处理
def process_data(data_path: str):
    adata = scvi.data.read_10x_multiome(data_path)
    adata.var_names_make_unique()

    n = 4004
    adata_rna = adata[:n, adata.var.modality == "Gene Expression"].copy()
    adata_paired = adata[n:2*n].copy()
    adata_atac = adata[2*n:, adata.var.modality == "Peaks"].copy()

    adata_mvi = scvi.data.organize_multiome_anndatas(adata_paired, adata_rna, adata_atac)
    adata_mvi = adata_mvi[:, adata_mvi.var["modality"].argsort()].copy()
    sc.pp.filter_genes(adata_mvi, min_cells=int(adata_mvi.shape[0] * 0.01))

    return adata_mvi

def process_satpathy_data(data_path: str):
    # 读取处理后的peak-by-cell矩阵和元数据文件
    adata = sc.read_10x_mtx(
        data_path,
        var_names='gene_symbols',
        cache=True
    )

    # 过滤数据，仅保留至少在0.1%的数据中检测到的peak
    sc.pp.filter_genes(adata, min_cells=int(adata.shape[0] * 0.001))

    # 使用UCSC liftover工具将这些peak从hg19提升到hg38基因组参考
    # 这一步需要在本地运行UCSC liftover工具，这里假设已经完成
    # 例如：liftOver hg19_peaks.bed hg19ToHg38.over.chain.gz hg38_peaks.bed unmapped

    return adata

# 训练 MultiVI 模型
def train_multivi(adata_mvi):
    scvi.model.MULTIVI.setup_anndata(adata_mvi, batch_key="modality")

    model = scvi.model.MULTIVI(
        adata_mvi,
        n_genes=(adata_mvi.var["modality"] == "Gene Expression").sum(),
        n_regions=(adata_mvi.var["modality"] == "Peaks").sum(),
    )

    # 将模型和数据移动到GPU
    if torch.cuda.is_available():
        model.to_device("cuda:0")
        adata_mvi.X = adata_mvi.X.cuda()

    model.train(max_epochs=100, early_stopping=50)

    return model

# 保存和加载模型
def save_load_model(model, model_dir: str, adata_mvi):
    model.save(model_dir, overwrite=True)
    return scvi.model.MULTIVI.load(model_dir, adata=adata_mvi)

# 提取和可视化潜在空间
def visualize_latent_space(adata_mvi, model):
    MULTIVI_LATENT_KEY = "X_multivi"
    adata_mvi.obsm[MULTIVI_LATENT_KEY] = model.get_latent_representation()
    sc.pp.neighbors(adata_mvi, use_rep=MULTIVI_LATENT_KEY)
    sc.tl.umap(adata_mvi, min_dist=0.2)
    sc.pl.umap(adata_mvi, color="modality")

# 插补缺失模态
def impute_missing_modalities(model, adata_mvi):
    imputed_expression = model.get_normalized_expression(adata_mvi, modality="Gene Expression")
    imputed_peaks = model.get_normalized_expression(adata_mvi, modality="Peaks")
    return imputed_expression, imputed_peaks

# 主函数
def main():
    setup()

    save_path = Path("D:/MultiVI/datasets")
    save_path.mkdir(parents=True, exist_ok=True)

    fname = "pbmc_10k"
    data_path = download_data(save_path, fname)

    adata_mvi = process_data(data_path)

    model = train_multivi(adata_mvi)

    model_dir = save_path / "multivi_model"

    loaded_model = save_load_model(model, model_dir, adata_mvi)

    visualize_latent_space(adata_mvi, loaded_model)

    imputed_expression, imputed_peaks = impute_missing_modalities(loaded_model, adata_mvi)

if __name__ == "__main__":
    main()