import os
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import anndata as ad
from scipy.io import mmread
from scipy.sparse import csr_matrix

# 设置随机种子
torch.manual_seed(420)

# 修改数据路径和输出文件名
data_directory = "D:\\MultiVI\\datasets\\share_skin"  # 修改目录名称
output_file = "share_skin_scAI.h5ad"  # 修改输出文件名

# 检查输入路径是否存在
if not os.path.exists(data_directory):
    print("输入数据路径不存在")
    exit(1)

# 更新文件路径为share_skin数据集
# RNA 数据路径
rna_count_path = os.path.join(data_directory, "share_skin_rna_normalize_count.mtx")  # 修改文件名
rna_genes_path = os.path.join(data_directory, "share_skin_scale_gene.txt")  # 修改文件名
rna_barcodes_path = os.path.join(data_directory, "share_skin_cell_barcode.txt")  # 修改文件名

# ATAC 数据路径
atac_count_path = os.path.join(data_directory, "share_skin_atac_normalize_count.mtx")  # 修改文件名
atac_peaks_path = os.path.join(data_directory, "share_skin_peak.txt")  # 修改文件名
atac_barcodes_path = os.path.join(data_directory, "share_skin_cell_barcode.txt")  # 修改文件名

# 加载 RNA 数据
cell_names = pd.read_csv(rna_barcodes_path, sep='\t', header=None, index_col=None)
cell_names.columns = ['cell_ids']
gene_names = pd.read_csv(rna_genes_path, sep='\t', header=None, index_col=None)
gene_names.columns = ['gene_ids']

# 加载 ATAC 数据
peak_names = pd.read_csv(atac_peaks_path, sep='\t', header=None, index_col=None)
peak_names.columns = ['peak_ids']

# 加载数据
X_rna = csr_matrix(mmread(rna_count_path).T)
X_atac = csr_matrix(mmread(atac_count_path).T)

# 转换为 PyTorch 张量
X_rna_tensor = torch.tensor(X_rna.toarray(), dtype=torch.float32)
X_atac_tensor = torch.tensor(X_atac.toarray(), dtype=torch.float32)

# 定义模型（保持不变）
class scAI(nn.Module):
    def __init__(self, input_dim_rna, input_dim_atac, latent_dim):
        super(scAI, self).__init__()
        self.encoder_rna = nn.Sequential(
            nn.Linear(input_dim_rna, 128),
            nn.ReLU(),
            nn.Linear(128, latent_dim)
        )
        self.encoder_atac = nn.Sequential(
            nn.Linear(input_dim_atac, 128),
            nn.ReLU(),
            nn.Linear(128, latent_dim)
        )
        self.decoder = nn.Sequential(
            nn.Linear(latent_dim, 128),
            nn.ReLU(),
            nn.Linear(128, input_dim_rna + input_dim_atac)
        )

    def forward(self, x_rna, x_atac):
        z_rna = self.encoder_rna(x_rna)
        z_atac = self.encoder_atac(x_atac)
        z = (z_rna + z_atac) / 2
        reconstructed = self.decoder(z)
        return reconstructed

# 初始化模型
input_dim_rna = X_rna.shape[1]
input_dim_atac = X_atac.shape[1]
latent_dim = 10
model = scAI(input_dim_rna, input_dim_atac, latent_dim)

# 使用 GPU
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
model.to(device)

# 定义损失函数和优化器
criterion = nn.MSELoss()
optimizer = optim.Adam(model.parameters(), lr=0.001)

# 数据加载器
train_dataset = TensorDataset(X_rna_tensor, X_atac_tensor)
train_loader = DataLoader(train_dataset, batch_size=64, shuffle=True)

# 训练模型
epochs = 100
for epoch in range(epochs):
    model.train()
    total_loss = 0
    for batch_idx, (x_rna, x_atac) in enumerate(train_loader):
        x_rna, x_atac = x_rna.to(device), x_atac.to(device)
        optimizer.zero_grad()
        reconstructed = model(x_rna, x_atac)
        loss = criterion(reconstructed, torch.cat([x_rna, x_atac], dim=1))
        loss.backward()
        optimizer.step()
        total_loss += loss.item()
    print(f"Epoch {epoch+1}/{epochs}, Loss: {total_loss:.4f}")

# 获取潜在表示
model.eval()
with torch.no_grad():
    latent = model.encoder_rna(X_rna_tensor.to(device)).cpu().numpy()

# 保存结果
adata = ad.AnnData(X=X_rna, obs=pd.DataFrame(index=cell_names.cell_ids), var=pd.DataFrame(index=gene_names.gene_ids))
adata.obsm["X_scAI"] = latent
adata.write(output_file)
print(f"结果已保存至 {output_file}")
