{"dataset_prefix": "paired_cellline", "data_preprocessing": {"min_rna_genes": 20, "min_atac_cells": 10, "n_top_genes": 4000, "n_top_peaks": 50000, "min_cell_percentage": 0.01}, "training_parameters": {"training_epochs": 500, "batch_size": 256, "learning_rate": 0.001, "weight_decay": 1e-06, "gradient_clip_val": 1.0}, "early_stopping": {"early_stopping": true, "early_stopping_patience": 50, "early_stopping_min_delta": 0.001, "check_val_every_n_epoch": 5}, "lr_scheduler": {"use_lr_scheduler": true, "lr_scheduler_type": "cosine", "lr_step_size": 100, "lr_gamma": 0.5, "lr_warmup_epochs": 10}, "model_architecture": {"n_latent": 20, "n_hidden": 256, "n_layers": 2, "dropout_rate": 0.1, "use_batch_norm": true}, "performance_optimization": {"use_mixed_precision": true, "pin_memory": true, "num_workers": 4}}