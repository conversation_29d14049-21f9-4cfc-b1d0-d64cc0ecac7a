#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
H5AD数据探索脚本
读取datasets/neuips/目录下的h5ad文件并输出详细信息
"""

import os
import numpy as np
import pandas as pd
import scanpy as sc
import matplotlib.pyplot as plt
import seaborn as sns

# 设置scanpy参数
sc.settings.verbosity = 1
sc.settings.set_figure_params(dpi=80, facecolor='white')

def explore_h5ad_file(file_path, file_name):
    """
    探索单个h5ad文件的详细信息
    
    参数:
        file_path: 文件路径
        file_name: 文件名称（用于显示）
    """
    print(f"\n{'='*60}")
    print(f"分析文件: {file_name}")
    print(f"路径: {file_path}")
    print(f"{'='*60}")
    
    try:
        # 读取数据
        adata = sc.read_h5ad(file_path)
        
        # 基本信息
        print(f"\n📊 基本数据信息:")
        print(f"   - 数据形状: {adata.shape} (细胞数 × 特征数)")
        print(f"   - 细胞数量: {adata.n_obs:,}")
        print(f"   - 特征数量: {adata.n_vars:,}")
        print(f"   - 数据类型: {type(adata.X)}")
        print(f"   - 是否稀疏矩阵: {'是' if hasattr(adata.X, 'nnz') else '否'}")
        
        if hasattr(adata.X, 'nnz'):
            sparsity = 1 - (adata.X.nnz / (adata.n_obs * adata.n_vars))
            print(f"   - 稀疏度: {sparsity:.2%}")
        
        # 观测数据 (细胞信息)
        print(f"\n🔬 细胞观测数据 (adata.obs):")
        print(f"   - 列数: {adata.obs.shape[1]}")
        if adata.obs.shape[1] > 0:
            print(f"   - 列名: {list(adata.obs.columns)}")
            
            # 详细分析每一列
            for col in adata.obs.columns:
                col_data = adata.obs[col]
                print(f"\n   📋 列 '{col}':")
                print(f"      - 数据类型: {col_data.dtype}")
                print(f"      - 非空值数量: {col_data.notna().sum():,}")
                print(f"      - 缺失值数量: {col_data.isna().sum():,}")
                
                if col_data.dtype == 'object' or col_data.dtype.name == 'category':
                    # 分类数据
                    unique_values = col_data.unique()
                    print(f"      - 唯一值数量: {len(unique_values)}")
                    if len(unique_values) <= 20:
                        print(f"      - 唯一值: {list(unique_values)}")
                        # 显示值计数
                        value_counts = col_data.value_counts()
                        print(f"      - 值分布:")
                        for val, count in value_counts.head(10).items():
                            print(f"        * {val}: {count:,} ({count/len(col_data)*100:.1f}%)")
                    else:
                        print(f"      - 前10个唯一值: {list(unique_values[:10])}")
                else:
                    # 数值数据
                    print(f"      - 最小值: {col_data.min():.4f}")
                    print(f"      - 最大值: {col_data.max():.4f}")
                    print(f"      - 平均值: {col_data.mean():.4f}")
                    print(f"      - 中位数: {col_data.median():.4f}")
        else:
            print("   - 无观测数据列")
        
        # 变量数据 (特征信息)
        print(f"\n🧬 特征变量数据 (adata.var):")
        print(f"   - 列数: {adata.var.shape[1]}")
        if adata.var.shape[1] > 0:
            print(f"   - 列名: {list(adata.var.columns)}")
            
            # 分析前几列
            for col in adata.var.columns[:5]:  # 只显示前5列以节省空间
                col_data = adata.var[col]
                print(f"\n   📋 列 '{col}':")
                print(f"      - 数据类型: {col_data.dtype}")
                print(f"      - 非空值数量: {col_data.notna().sum():,}")
                
                if col_data.dtype == 'object' or col_data.dtype.name == 'category':
                    unique_values = col_data.unique()
                    print(f"      - 唯一值数量: {len(unique_values)}")
                    if len(unique_values) <= 10:
                        print(f"      - 唯一值: {list(unique_values)}")
                else:
                    print(f"      - 数值范围: [{col_data.min():.4f}, {col_data.max():.4f}]")
        else:
            print("   - 无变量数据列")
        
        # 多维观测数据
        print(f"\n📈 多维观测数据 (adata.obsm):")
        if len(adata.obsm.keys()) > 0:
            print(f"   - 键数量: {len(adata.obsm.keys())}")
            for key in adata.obsm.keys():
                data_shape = adata.obsm[key].shape
                print(f"   - '{key}': 形状 {data_shape}")
        else:
            print("   - 无多维观测数据")
        
        # 非结构化数据
        print(f"\n📝 非结构化数据 (adata.uns):")
        if len(adata.uns.keys()) > 0:
            print(f"   - 键数量: {len(adata.uns.keys())}")
            for key in adata.uns.keys():
                data_type = type(adata.uns[key])
                print(f"   - '{key}': {data_type}")
        else:
            print("   - 无非结构化数据")
        
        # 数据质量检查
        print(f"\n🔍 数据质量检查:")
        
        # 检查表达矩阵
        if hasattr(adata.X, 'data'):
            # 稀疏矩阵
            has_nan = np.isnan(adata.X.data).any()
            has_inf = np.isinf(adata.X.data).any()
            min_val = adata.X.data.min()
            max_val = adata.X.data.max()
        else:
            # 密集矩阵
            has_nan = np.isnan(adata.X).any()
            has_inf = np.isinf(adata.X).any()
            min_val = adata.X.min()
            max_val = adata.X.max()
        
        print(f"   - 包含NaN值: {'是' if has_nan else '否'}")
        print(f"   - 包含无穷大值: {'是' if has_inf else '否'}")
        print(f"   - 数值范围: [{min_val:.4f}, {max_val:.4f}]")
        
        # 批次信息分析（如果存在）
        if 'batch' in adata.obs.columns:
            print(f"\n🏷️  批次信息详细分析:")
            batch_info = adata.obs['batch'].value_counts().sort_index()
            print(f"   - 总批次数: {len(batch_info)}")
            print(f"   - 批次分布:")
            for batch, count in batch_info.items():
                percentage = count / len(adata.obs) * 100
                print(f"     * {batch}: {count:,} 细胞 ({percentage:.1f}%)")
        
        # 细胞类型信息分析（如果存在）
        if 'cell_type' in adata.obs.columns:
            print(f"\n🧬 细胞类型信息详细分析:")
            celltype_info = adata.obs['cell_type'].value_counts()
            print(f"   - 总细胞类型数: {len(celltype_info)}")
            print(f"   - 细胞类型分布 (前10个):")
            for celltype, count in celltype_info.head(10).items():
                percentage = count / len(adata.obs) * 100
                print(f"     * {celltype}: {count:,} 细胞 ({percentage:.1f}%)")
        
        return adata
        
    except Exception as e:
        print(f"❌ 读取文件时出错: {str(e)}")
        return None

def create_summary_visualization(adata_rna, adata_atac, output_dir="data_exploration_results"):
    """
    创建数据概览的可视化图表
    """
    print(f"\n📊 创建数据概览可视化...")
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # 1. 批次分布对比
    if adata_rna is not None and 'batch' in adata_rna.obs.columns:
        rna_batch_counts = adata_rna.obs['batch'].value_counts()
        axes[0,0].bar(range(len(rna_batch_counts)), rna_batch_counts.values, 
                     alpha=0.7, color='skyblue', label='RNA')
        axes[0,0].set_xlabel('批次')
        axes[0,0].set_ylabel('细胞数量')
        axes[0,0].set_title('RNA数据批次分布')
        axes[0,0].set_xticks(range(len(rna_batch_counts)))
        axes[0,0].set_xticklabels(rna_batch_counts.index, rotation=45)
        
        # 添加数值标签
        for i, v in enumerate(rna_batch_counts.values):
            axes[0,0].text(i, v + max(rna_batch_counts.values) * 0.01, str(v), 
                          ha='center', va='bottom')
    
    if adata_atac is not None and 'batch' in adata_atac.obs.columns:
        atac_batch_counts = adata_atac.obs['batch'].value_counts()
        axes[0,1].bar(range(len(atac_batch_counts)), atac_batch_counts.values, 
                     alpha=0.7, color='lightcoral', label='ATAC')
        axes[0,1].set_xlabel('批次')
        axes[0,1].set_ylabel('细胞数量')
        axes[0,1].set_title('ATAC数据批次分布')
        axes[0,1].set_xticks(range(len(atac_batch_counts)))
        axes[0,1].set_xticklabels(atac_batch_counts.index, rotation=45)
        
        # 添加数值标签
        for i, v in enumerate(atac_batch_counts.values):
            axes[0,1].text(i, v + max(atac_batch_counts.values) * 0.01, str(v), 
                          ha='center', va='bottom')
    
    # 2. 细胞类型分布
    if adata_rna is not None and 'cell_type' in adata_rna.obs.columns:
        celltype_counts = adata_rna.obs['cell_type'].value_counts().head(10)
        axes[0,2].barh(range(len(celltype_counts)), celltype_counts.values, 
                      alpha=0.7, color='lightgreen')
        axes[0,2].set_xlabel('细胞数量')
        axes[0,2].set_ylabel('细胞类型')
        axes[0,2].set_title('细胞类型分布 (前10个)')
        axes[0,2].set_yticks(range(len(celltype_counts)))
        axes[0,2].set_yticklabels(celltype_counts.index)
    
    # 3. 数据规模对比
    if adata_rna is not None and adata_atac is not None:
        categories = ['细胞数', '特征数']
        rna_values = [adata_rna.n_obs, adata_rna.n_vars]
        atac_values = [adata_atac.n_obs, adata_atac.n_vars]
        
        x = np.arange(len(categories))
        width = 0.35
        
        axes[1,0].bar(x - width/2, rna_values, width, label='RNA', alpha=0.7, color='skyblue')
        axes[1,0].bar(x + width/2, atac_values, width, label='ATAC', alpha=0.7, color='lightcoral')
        axes[1,0].set_xlabel('数据维度')
        axes[1,0].set_ylabel('数量')
        axes[1,0].set_title('RNA vs ATAC 数据规模对比')
        axes[1,0].set_xticks(x)
        axes[1,0].set_xticklabels(categories)
        axes[1,0].legend()
        axes[1,0].set_yscale('log')  # 使用对数刻度
        
        # 添加数值标签
        for i, (rna_val, atac_val) in enumerate(zip(rna_values, atac_values)):
            axes[1,0].text(i - width/2, rna_val * 1.1, f'{rna_val:,}', 
                          ha='center', va='bottom', rotation=45)
            axes[1,0].text(i + width/2, atac_val * 1.1, f'{atac_val:,}', 
                          ha='center', va='bottom', rotation=45)
    
    # 4. 批次-细胞类型交叉分析
    if (adata_rna is not None and 'batch' in adata_rna.obs.columns and 
        'cell_type' in adata_rna.obs.columns):
        
        # 创建交叉表
        cross_table = pd.crosstab(adata_rna.obs['batch'], adata_rna.obs['cell_type'])
        
        # 选择前8个最常见的细胞类型
        top_celltypes = adata_rna.obs['cell_type'].value_counts().head(8).index
        cross_table_subset = cross_table[top_celltypes]
        
        # 创建堆叠条形图
        cross_table_subset.plot(kind='bar', stacked=True, ax=axes[1,1], 
                               alpha=0.8, colormap='tab20')
        axes[1,1].set_xlabel('批次')
        axes[1,1].set_ylabel('细胞数量')
        axes[1,1].set_title('批次-细胞类型分布')
        axes[1,1].tick_params(axis='x', rotation=45)
        axes[1,1].legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    
    # 5. 数据质量概览
    quality_metrics = []
    quality_values = []
    
    if adata_rna is not None:
        # RNA数据质量指标
        if hasattr(adata_rna.X, 'data'):
            rna_sparsity = 1 - (adata_rna.X.nnz / (adata_rna.n_obs * adata_rna.n_vars))
        else:
            rna_sparsity = np.mean(adata_rna.X == 0)
        
        quality_metrics.extend(['RNA稀疏度', 'RNA特征数'])
        quality_values.extend([rna_sparsity, adata_rna.n_vars])
    
    if adata_atac is not None:
        # ATAC数据质量指标
        if hasattr(adata_atac.X, 'data'):
            atac_sparsity = 1 - (adata_atac.X.nnz / (adata_atac.n_obs * adata_atac.n_vars))
        else:
            atac_sparsity = np.mean(adata_atac.X == 0)
        
        quality_metrics.extend(['ATAC稀疏度', 'ATAC特征数'])
        quality_values.extend([atac_sparsity, adata_atac.n_vars])
    
    if quality_metrics:
        # 分别处理稀疏度和特征数（不同量级）
        sparsity_metrics = [m for m in quality_metrics if '稀疏度' in m]
        sparsity_values = [quality_values[i] for i, m in enumerate(quality_metrics) if '稀疏度' in m]
        
        if sparsity_metrics:
            axes[1,2].bar(sparsity_metrics, sparsity_values, alpha=0.7, color='orange')
            axes[1,2].set_ylabel('稀疏度')
            axes[1,2].set_title('数据稀疏度对比')
            axes[1,2].tick_params(axis='x', rotation=45)
            
            # 添加百分比标签
            for i, v in enumerate(sparsity_values):
                axes[1,2].text(i, v + max(sparsity_values) * 0.01, f'{v:.1%}', 
                              ha='center', va='bottom')
    
    plt.tight_layout()
    
    # 保存图表
    output_path = os.path.join(output_dir, 'data_overview.png')
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"   ✅ 可视化图表已保存到: {output_path}")

def main():
    """主函数"""
    print("🔍 H5AD数据探索工具")
    print("=" * 60)
    
    # 设置文件路径
    data_dir = "../datasets/neuips"
    rna_path = os.path.join(data_dir, "RNA_unpred.h5ad")
    atac_path = os.path.join(data_dir, "ATAC_unpred.h5ad")
    
    # 探索RNA数据
    adata_rna = explore_h5ad_file(rna_path, "RNA_unpred.h5ad")
    
    # 探索ATAC数据
    adata_atac = explore_h5ad_file(atac_path, "ATAC_unpred.h5ad")
    
    # 创建可视化
    if adata_rna is not None or adata_atac is not None:
        create_summary_visualization(adata_rna, adata_atac)
    
    # 总结
    print(f"\n{'='*60}")
    print("📋 数据探索总结:")
    print(f"{'='*60}")
    
    if adata_rna is not None:
        print(f"✅ RNA数据: {adata_rna.n_obs:,} 细胞 × {adata_rna.n_vars:,} 基因")
        if 'batch' in adata_rna.obs.columns:
            print(f"   - 批次数: {len(adata_rna.obs['batch'].unique())}")
        if 'cell_type' in adata_rna.obs.columns:
            print(f"   - 细胞类型数: {len(adata_rna.obs['cell_type'].unique())}")
    
    if adata_atac is not None:
        print(f"✅ ATAC数据: {adata_atac.n_obs:,} 细胞 × {adata_atac.n_vars:,} 峰")
        if 'batch' in adata_atac.obs.columns:
            print(f"   - 批次数: {len(adata_atac.obs['batch'].unique())}")
        if 'cell_type' in adata_atac.obs.columns:
            print(f"   - 细胞类型数: {len(adata_atac.obs['cell_type'].unique())}")
    
    print(f"\n🎯 建议的批次效应分析:")
    if adata_rna is not None and 'batch' in adata_rna.obs.columns:
        available_batches = list(adata_rna.obs['batch'].unique())
        print(f"   - 可用批次: {available_batches}")
        if len(available_batches) >= 3:
            suggested_batches = available_batches[:3]
            print(f"   - 建议选择批次: {suggested_batches}")
        else:
            print(f"   - 所有批次: {available_batches}")

if __name__ == "__main__":
    main()
