@article{ashuach2023multivi,
  title={MultiVI: deep generative model for the integration of multi-modal data},
  author={<PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON><PERSON>, <PERSON><PERSON>},
  journal={Nature Methods},
  volume={20},
  number={3},
  pages={435--442},
  year={2023},
  publisher={Nature Publishing Group US}
}

@article{stuart2019comprehensive,
  title={Comprehensive integration of single-cell data},
  author={<PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON>},
  journal={Cell},
  volume={177},
  number={7},
  pages={1888--1902},
  year={2019},
  publisher={Elsevier}
}

@article{argelaguet2020mofa,
  title={MOFA+: a statistical framework for comprehensive integration of multi-modal single-cell data},
  author={<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON>, <PERSON> and <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON>},
  journal={Genome biology},
  volume={21},
  number={1},
  pages={1--17},
  year={2020},
  publisher={BioMed Central}
}

@article{zuo2021scai,
  title={scAI: an unsupervised approach for the integrative analysis of parallel single-cell transcriptomic and epigenomic profiles},
  author={Zuo, Chengwei and Chen, Luonan},
  journal={Genome biology},
  volume={22},
  number={1},
  pages={1--19},
  year={2021},
  publisher={BioMed Central}
}

@article{ma2020scmvp,
  title={scMVP: A cell-by-cell deep learning framework for multi-omic data integration},
  author={Ma, Dongfang and Wang, Zhenqin},
  journal={bioRxiv},
  year={2020},
  publisher={Cold Spring Harbor Laboratory},
  doi={10.1101/2020.08.05.238758}
}

@article{hao2021integrated,
  title={Integrated analysis of multimodal single-cell data},
  author={Hao, Yuhan and Hao, Stephanie and Andersen-Nissen, Erica and Mauck III, William M and Zheng, Shiwei and Butler, Andrew and Lee, Maddie J and Wilk, Aaron J and Darby, Charlotte and Zager, Michael and others},
  journal={Cell},
  volume={184},
  number={13},
  pages={3573--3587},
  year={2021},
  publisher={Elsevier}
}

@article{luecken2021benchmarking,
  title={Benchmarking atlas-level data integration in single-cell genomics},
  author={Luecken, Malte D and B{\"u}ttner, Maren and Chaichoompu, Kridsadakorn and Danese, Anna and Interlandi, Marta and Mueller, Michaela F and Strobl, Daniel C and Zappia, Luke and Dugas, Martin and Colom{\'e}-Tatch{\'e}, Maria and others},
  journal={Nature methods},
  volume={19},
  number={1},
  pages={41--50},
  year={2022},
  publisher={Nature Publishing Group}
}

@article{cao2018joint,
  title={Joint profiling of chromatin accessibility and gene expression in thousands of single cells},
  author={Cao, Junyue and Cusanovich, Darren A and Ramani, Vijay and Aghamirzaie, Delasa and Pliner, Hannah A and Hill, Andrew J and Daza, Riza M and McFaline-Figueroa, Jose L and Packer, Jonathan S and Christiansen, Lena and others},
  journal={Science},
  volume={361},
  number={6409},
  pages={1380--1385},
  year={2018},
  publisher={American Association for the Advancement of Science}
}

@article{chen2019snare,
  title={SNARE-seq reveals direct-capture Hi-C of chromatin compartments, states, and territories along with RNA for nuclear cartography},
  author={Chen, Sanjay and Lake, Blue B and Zhang, Kun},
  journal={Nature biotechnology},
  volume={37},
  number={8},
  pages={931--934},
  year={2019},
  publisher={Nature Publishing Group}
}

@article{swanson2021tea,
  title={TEA-seq: a trimodal assay for integrated single cell measurement of transcription, epitopes, and chromatin accessibility},
  author={Swanson, Emily and Lord, Christopher and Reading, Joshua and Heubeck, Alexander T and Genge, Peter C and Thomson, Zachary and Weiss, Matthew D and Li, Xiao-jun and Savage, Alison K and Green, Richard R and others},
  journal={Elife},
  volume={10},
  pages={e63632},
  year={2021},
  publisher={eLife Sciences Publications Limited}
}

@article{kingma2013auto,
  title={Auto-encoding variational bayes},
  author={Kingma, Diederik P and Welling, Max},
  journal={arXiv preprint arXiv:1312.6114},
  year={2013}
}

@article{wolf2018scanpy,
  title={SCANPY: large-scale single-cell gene expression data analysis},
  author={Wolf, F Alexander and Angerer, Philipp and Theis, Fabian J},
  journal={Genome biology},
  volume={19},
  number={1},
  pages={1--5},
  year={2018},
  publisher={BioMed Central}
}

@article{paszke2019pytorch,
  title={Pytorch: An imperative style, high-performance deep learning library},
  author={Paszke, Adam and Gross, Sam and Massa, Francisco and Lerer, Adam and Bradbury, James and Chanan, Gregory and Killeen, Trevor and Lin, Zeming and Gimelshein, Natalia and Antiga, Luca and others},
  journal={Advances in neural information processing systems},
  volume={32},
  year={2019}
}

@article{hubert1985comparing,
  title={Comparing partitions},
  author={Hubert, Lawrence and Arabie, Phipps},
  journal={Journal of classification},
  volume={2},
  number={1},
  pages={193--218},
  year={1985},
  publisher={Springer}
}

@article{strehl2002cluster,
  title={Cluster ensembles---a knowledge reuse framework for combining multiple partitions},
  author={Strehl, Alexander and Ghosh, Joydeep},
  journal={Journal of machine learning research},
  volume={3},
  number={Dec},
  pages={583--617},
  year={2002}
}

@article{korsunsky2019harmony,
  title={Fast, sensitive and accurate integration of single-cell data with Harmony},
  author={Korsunsky, Ilya and Millard, Nghia and Fan, Jean and Slowikowski, Kamil and Zhang, Fan and Wei, Kevin and Baglaenko, Yuriy and Brenner, Michael and Loh, Po-ru and Raychaudhuri, Soumya},
  journal={Nature methods},
  volume={16},
  number={12},
  pages={1289--1296},
  year={2019},
  publisher={Nature Publishing Group}
}
